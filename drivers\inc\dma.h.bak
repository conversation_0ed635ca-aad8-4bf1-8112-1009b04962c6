#ifndef __DMA_H__
#define __DMA_H__

#include "common.h"
#include "l2c.h"

/* DMAC instruction set */
#define DMAI_ADDHW_SRC(x)      /* DMAADDH       */ (((x) << 8) | 0x54)
#define DMAI_ADDHW_DST(x)      /* DMAADDH       */ (((x) << 8) | 0x56)

#define DMAI_END               /* DMAEND        */ (0x00)

#define DMAI_FLUSH_PERIPH(x)   /* DMAFLUSHP     */ (((x) << 11) | 0x35)

#define DMAKILL                /* DMAKILL       */ (0x01)

#define DMAI_LD                /* DMALD[S|B]    */ (0x04)
#define DMAI_LDS               /* DMALD[S|B]    */ (DMAI_LD | 0x1)
#define DMAI_LDB               /* DMALD[S|B]    */ (DMAI_LD | 0x3)

#define DMAI_LDPS(p)           /* DMALDP<S|B>   */ (((p) << 11) | 0x25)
#define DMAI_LDPB(p)           /* DMALDP<S|B>   */ (DMAI_LDPS(p) | 0x2)

#define DMAI_LP(lc, i)         /* DMALP         */ (((i) <<  8) | 0x20 | ((lc) << 1))

#define DMAI_LPFEEND(lc, jmp)  /* DMALPEND[S|B] */ ((jmp << 8) | 0x28 | ((lc) << 2))
#define DMAI_LPEND(lc, jmp)    /* DMALPEND[S|B] */ (DMAI_LPFEEND(lc, jmp) | 0x10)
#define DMAI_LPENDS(lc, jmp)   /* DMALPEND[S|B] */ (DMAI_LPEND(lc, jmp) | 0x1)
#define DMAI_LPENDB(lc, jmp)   /* DMALPEND[S|B] */ (DMAI_LPEND(lc, jmp) | 0x3)
#define DMAI_LPFEENDS(lc, jmp) /* DMALPEND[S|B] */ (DMAI_LPFEEND(lc, jmp) | 0x1)
#define DMAI_LPFEENDB(lc, jmp) /* DMALPEND[S|B] */ (DMAI_LPFEEND(lc, jmp) | 0x3)

/* DMALPFE       */

#define DMAI_NOP               /* DMANOP        */ (0x18)

#define DMAI_RMB               /* DMARMB        */ (0x12)

#define DMAI_SEV(e)            /* DMASEV        */ (((e) << 11) | 0x34)

#define DMAI_ST                /* DMAST[S|B]    */ (0x08)
#define DMAI_STS               /* DMAST[S|B]    */ (0x09)
#define DMAI_STB               /* DMAST[S|B]    */ (0x0B)

#define DMAI_STPS(e)           /* DMASTP<S|B>   */ (((e)<<11)|0x29)
#define DMAI_STPB(e)           /* DMASTP<S|B>   */ (((e)<<11)|0x2B)

#define DMAI_STZ               /* DMASTZ        */ (0x0C)

#define DMAI_WFE(e)            /* DMAWFE        */ (((e)<<11)|0x036)

#define DMAI_WFP(p)            /* DMAWFP<S|B|P> */ ((p)<<11|0x31)
#define DMAI_WFPB(p)           /* DMAWFP<S|B|P> */ ((p)<<11|0x32)
#define DMAI_WFPS(p)           /* DMAWFP<S|B|P> */ ((p)<<11|0x30)

#define DMAI_WMB               /* DMAWMB        */ (0x13)
#define DMAI_GO(pc, ch, ns)    /* DMAGO         */ (pc<<16|(ch<<8)|0xA0|(ns<<1))

/* DMAC instruction sizes */
#define DMAISZ_ADDHW_SRC         3
#define DMAISZ_ADDHW_DST         3
#define DMAISZ_END               1
#define DMAISZ_FLUSH_PERIPH      2
#define DMAISZ_GO                6
#define DMAISZ_NS_GO             6
#define DMAISZ_LD                1
#define DMAISZ_LDS               1
#define DMAISZ_LDB               1
#define DMAISZ_LDPS              2
#define DMAISZ_LDPB              2
#define DMAISZ_LP                2
#define DMAISZ_LPEND             2
#define DMAISZ_LPENDS            2
#define DMAISZ_LPENDB            2
/* DMALPFE       */
#define DMAISZ_KILL              1
#define DMAISZ_MOV_SAR           6
#define DMAISZ_MOV_DAR           6
#define DMAISZ_MOV_CCR           6
#define DMAISZ_NOP               1
#define DMAISZ_RMB               1
#define DMAISZ_SEV               2
#define DMAISZ_ST                1
#define DMAISZ_STS               1
#define DMAISZ_STB               1
#define DMAISZ_STPS              2
#define DMAISZ_STPB              2
#define DMAISZ_STZ               1
#define DMAISZ_WFE               2
#define DMAISZ_WFP               2
#define DMAISZ_WFPB              2
#define DMAISZ_WFPS              2
#define DMAISZ_WMB               1

#define CH_0   0
#define CH_1   1
#define CH_2   2
#define CH_3   3
#define CH_4   4
#define CH_5   5
#define CH_6   6
#define CH_7   7

#define USE_IRQ       1
#define USE_EVENT     0

#define MANAGER_THREAD  0
#define CHANNEL_THREAD  1

//----------------------------------------
//
//  Register Define
//
//---------------------------------------
//
// ips2_config | DMAC_CONFIG
//
typedef struct
{
  __IO uint32_t  BOOT_FROM_PC;     // 0x000
  __IO uint32_t  BOOT_ADDR;        // 0x004
  __IO uint32_t  BOOT_MANAGER_NS;  // 0x008
  __IO uint32_t  BOOT_IRQ_NS;      // 0x00c
  __IO uint32_t  BOOT_PERIPH_NS;   // 0x010
} DMAC_CONFIG_TypeDef;

//
// DMA Reg
//
#define N1    (0x020 - 0x008) >> 2
#define N3    (0x100 - 0x060) >> 2
#define N4    (0x400 - 0x140) >> 2
#define C0    (0x420 - 0x414) >> 2
#define C1    (0x440 - 0x434) >> 2
#define C2    (0x460 - 0x454) >> 2
#define C3    (0x480 - 0x474) >> 2
#define C4    (0x4A0 - 0x494) >> 2
#define C5    (0x4C0 - 0x4B4) >> 2
#define C6    (0x4E0 - 0x4D4) >> 2
#define C7    (0x500 - 0x4F4) >> 2
#define N5    (0xD00 - 0x500) >> 2
#define N6    (0xE00 - 0xD10) >> 2
#define N7    (0xE80 - 0xE18) >> 2
#define N8    (0xFE0 - 0xE84) >> 2


typedef struct
{
  // DMAC control register
  __IO uint32_t DSR;               // RO 0x000
  __IO uint32_t DPC;               // RO 0x004
  __IO uint32_t REVERSED_N1[N1];   //    0x008 ~ 0x01C
  __IO uint32_t INTEN;             // RW 0x020           RW
  __IO uint32_t INT_EVENT_RIS;     // RO 0x024
  __IO uint32_t INTMIS;            // RO 0x028
  __IO uint32_t INTCLR;            // WO 0x02C           WO
  __IO uint32_t FSRD;              // RO 0x030
  __IO uint32_t FSRC;              // RO 0x034
  __IO uint32_t FTRD;              // RO 0x038
  __IO uint32_t REVERSED_2;        //    0x03C
  // Fault Type DMA channel reg
  __IO uint32_t FTR0;              // RO 0x040
  __IO uint32_t FTR1;              // RO 0x044
  __IO uint32_t FTR2;              // RO 0x048
  __IO uint32_t FTR3;              // RO 0x04C
  __IO uint32_t FTR4;              // RO 0x050
  __IO uint32_t FTR5;              // RO 0x054
  __IO uint32_t FTR6;              // RO 0x058
  __IO uint32_t FTR7;              // RO 0x05C
  __IO uint32_t REVERSED_N3[N3];   //    0x060 ~ 0x0FC

  // DMAC channel thread status register
  // Channel Status PC reg
  __IO uint32_t CSR0;              // RO 0x100
  __IO uint32_t CPC0;              // RO 0x104
  __IO uint32_t CSR1;              // RO 0x108
  __IO uint32_t CPC1;              // RO 0x10C 
  __IO uint32_t CSR2;              // RO 0x110
  __IO uint32_t CPC2;              // RO 0x114
  __IO uint32_t CSR3;              // RO 0x118
  __IO uint32_t CPC3;              // RO 0x11C
  __IO uint32_t CSR4;              // RO 0x120
  __IO uint32_t CPC4;              // RO 0x124
  __IO uint32_t CSR5;              // RO 0x128
  __IO uint32_t CPC5;              // RO 0x12C
  __IO uint32_t CSR6;              // RO 0x130
  __IO uint32_t CPC6;              // RO 0x134
  __IO uint32_t CSR7;              // RO 0x138
  __IO uint32_t CPC7;              // RO 0x13C
  __IO uint32_t REVERSED_N4[N4];   //     0x140 ~ 0x3FC

  // AXI status and loop counter register
  __IO uint32_t SAR0;              // RO 0x400
  __IO uint32_t DAR0;              // RO 0x404
  __IO uint32_t CCR0;              // RO 0x408
  __IO uint32_t LC0_0;             // RO 0x40C
  __IO uint32_t LC1_0;             // RO 0x410
  __IO uint32_t REVERSED_C0[C0];   //    0x414 ~ 0x41C
  __IO uint32_t SAR1;              // RO 0x420
  __IO uint32_t DAR1;              // RO 0x424
  __IO uint32_t CCR1;              // RO 0x428
  __IO uint32_t LC0_1;             // RO 0x42C
  __IO uint32_t LC1_1;             // RO 0x430
  __IO uint32_t REVERSED_C1[C1];   //    0x434 ~ 0x43C
  __IO uint32_t SAR2;              // RO 0x440  
  __IO uint32_t DAR2;              // RO 0x444  
  __IO uint32_t CCR2;              // RO 0x448  
  __IO uint32_t LC0_2;             // RO 0x44C
  __IO uint32_t LC1_2;             // RO 0x450
  __IO uint32_t REVERSED_C2[C2];   //    0x454 ~ 0x45C
  __IO uint32_t SAR3;              // RO 0x460   
  __IO uint32_t DAR3;              // RO 0x464   
  __IO uint32_t CCR3;              // RO 0x468   
  __IO uint32_t LC0_3;             // RO 0x46C
  __IO uint32_t LC1_3;             // RO 0x470
  __IO uint32_t REVERSED_C3[C3];   //    0x474 ~ 0x47C
  __IO uint32_t SAR4;              // RO 0x480  
  __IO uint32_t DAR4;              // RO 0x484  
  __IO uint32_t CCR4;              // RO 0x488  
  __IO uint32_t LC0_4;             // RO 0x48C
  __IO uint32_t LC1_4;             // RO 0x490
  __IO uint32_t REVERSED_C4[C4];   //    0x494 ~ 0x49C
  __IO uint32_t SAR5;              // RO 0x4A0  
  __IO uint32_t DAR5;              // RO 0x4A4  
  __IO uint32_t CCR5;              // RO 0x4A8  
  __IO uint32_t LC0_5;             // RO 0x4AC
  __IO uint32_t LC1_5;             // RO 0x4B0
  __IO uint32_t REVERSED_C5[C5];   //    0x4B4 ~ 0x4BC
  __IO uint32_t SAR6;              // RO 0x4C0 
  __IO uint32_t DAR6;              // RO 0x4C4 
  __IO uint32_t CCR6;              // RO 0x4C8 
  __IO uint32_t LC0_6;             // RO 0x4CC
  __IO uint32_t LC1_6;             // RO 0x4D0
  __IO uint32_t REVERSED_C6[C6];   //    0x4D4 ~ 0x4DC
  __IO uint32_t SAR7;              // RO 0x4E0 
  __IO uint32_t DAR7;              // RO 0x4E4     
  __IO uint32_t CCR7;              // RO 0x4E8 
  __IO uint32_t LC0_7;             // RO 0x4EC
  __IO uint32_t LC1_7;             // RO 0x4F0
  __IO uint32_t REVERSED_C7[C7];   //    0x4F4 ~ 0x4FC
  __IO uint32_t REVERSED_N5[N5];   //    0x500 ~ 0xCFC

  // DMAC debug reg
  __IO uint32_t DBGSTATUS;         // RO 0xD00
  __IO uint32_t DBGCMD;            // WO 0xD04            WO
  __IO uint32_t DBGINST0;          // WO 0xD08            WO
  __IO uint32_t DBGINST1;          // WO 0xD0C            WO
  __IO uint32_t REVERSED_N6[N6];   //    0xD10 ~ 0xDFC

  // DMAC config reg
  __IO uint32_t CR0;               // RO 0xE00
  __IO uint32_t CR1;               // RO 0xE04
  __IO uint32_t CR2;               // RO 0xE08
  __IO uint32_t CR3;               // RO 0xE0C
  __IO uint32_t CR4;               // RO 0xE10
  __IO uint32_t CRD;               // RO 0xE14
  __IO uint32_t REVERSED_N7[N7];   //    0xE18 ~ 0xE7C
  __IO uint32_t WD;                // RW 0xE80            RW
  __IO uint32_t REVERSED_N8[N8];   //    0xE84 ~ 0xFDC

} DMAC_TypeDef;

//----------------------------------------
//
//  Register Address Define
//
//---------------------------------------

#define  DMAC_SECURE_BASE      DMAC_BASE
#define  DMAC_NON_SECURE_BASE  (DMAC_BASE + 0x1000)

#define  DMAC_NON_SECURE_REG   ((DMAC_TypeDef *)        DMAC_NON_SECURE_BASE)
#define  DMAC_SECURE_REG       ((DMAC_TypeDef *)        DMAC_SECURE_BASE)
#define  DMAC_CONFIG_REG       ((DMAC_CONFIG_TypeDef *) DMAC_CONFIG_BASE)

#define NON_SECURE  1
#define SECURE      0

#define NO_SWAP     0
#define SWAP_IN_8   0
#define SWAP_IN_16  1
#define SWAP_IN_32  2
#define SWAP_IN_64  3
#define SWAP_IN_128 4

#define STATE_STOPPED           (1<<0)
#define STATE_EXECUTING         (1<<1)
#define STATE_WFE               (1<<2)
#define STATE_FAULTING          (1<<3)
#define STATE_COMPLETING        (1<<4)
#define STATE_WFP               (1<<5)
#define STATE_KILLING           (1<<6)
#define STATE_FAULT_COMPLETING  (1<<7)
#define STATE_CACHEMISS         (1<<8)
#define STATE_UPDTPC            (1<<9)
#define STATE_ATBARRIER         (1<<10)
#define STATE_QUEUEBUSY         (1<<11)
#define STATE_INVALID           (1<<15)

typedef enum                   
{                               
     EVENT_IRQ_0 = (0x1<<0),
     EVENT_IRQ_1 = (0x1<<1),
     EVENT_IRQ_2 = (0x1<<2),
     EVENT_IRQ_3 = (0x1<<3),
     EVENT_IRQ_4 = (0x1<<4),
     EVENT_IRQ_5 = (0x1<<5),
     EVENT_IRQ_6 = (0x1<<6),
     EVENT_IRQ_7 = (0x1<<7),
	 EVENT_IRQ_8 = (0x1<<8),
	 EVENT_IRQ_9 = (0x1<<9),
	 EVENT_IRQ_10 = (0x1<<10),
	 EVENT_IRQ_11 = (0x1<<11),
	 EVENT_IRQ_12 = (0x1<<12),
	 EVENT_IRQ_13 = (0x1<<13),
	 EVENT_IRQ_14 = (0x1<<14),
	 EVENT_IRQ_15 = (0x1<<15),
     NON_EVENT_IRQ = (0x1<<16)
} EVENT_IRQn;

typedef enum
{
    PERIPH_0 = (0x1<<0),
    PERIPH_1 = (0x1<<1),
    PERIPH_2 = (0x1<<2),
    PERIPH_3 = (0x1<<3),
    PERIPH_4 = (0x1<<4),
    PERIPH_5 = (0x1<<5),
    PERIPH_6 = (0x1<<6),
    PERIPH_7 = (0x1<<7),
    NON_PERIPH = (0x1<<8)
} PERPPHn;

//NOTE:1.the secure state of event_irq & periph is base param manager_state
//     2.please refer to upper definitions to determine param event_irq & periph        
//initialize DMAC
void 
dma_init
(
    uint8_t manager_state,    //after exit from reset, config manager state 1(non-secure),0(secure state)
    EVENT_IRQn event_irq,     //after exit from reset,config secure state of corresponding event/irq
    PERPPHn periph            //after exit from reset,config secure state of corresponding periph
);

//-----------------------------------------------------------------------
//
//                         DMA Config 
//
//-----------------------------------------------------------------------

void 
enable_resetn_dma(void);

void
disable_resetn_dma(void);

void
enable_boot_from_pc(void);

void
disable_boot_from_pc(void);

void
enable_boot_manager_ns(void);

void
disable_boot_manager_ns(void);

void
enable_boot_irq_ns
(
  EVENT_IRQn event_irq_num
);

void
disable_boot_irq_ns
(
  EVENT_IRQn event_irq_num
);

void
enable_boot_periph_ns
(
  PERPPHn periph_num
);

void
disable_boot_periph_ns
(
  PERPPHn periph_num
);

uint8_t
get_dma_irq_abort(void);


//-----------------------------------------------------------------------
//
//                         DMA Function
//
//-----------------------------------------------------------------------

void 
set_irq_enable
(
  uint8_t ns,
  uint8_t event_irq_num,
  uint8_t event_irq        // 1: irq  0: event
);

void
clear_irq
(
   uint8_t ns,
   uint8_t event_irq_num
);

uint8_t
get_event_irq_raw_status
(
   uint8_t ns,             // select apb port
   uint8_t event_irq_num
);

uint8_t 
get_manager_fault_status
(
  uint8_t ns      // select apb port
);

uint8_t
get_channel_fault_status
(
  uint8_t ns,
  uint8_t channel
);

uint32_t
get_manager_fault_type
(
  uint8_t ns
);

uint32_t
get_channel_fault_type
(
  uint8_t ns,
  uint8_t channel
);

//-------------------------------
// get debug status reg
//
uint32_t
get_debug_status
(
  uint8_t ns
);


//----------------------------
//  set debug cmd reg 
//
void 
debug_go
(
  uint8_t ns
);

//----------------------------
// dbginst0 and dbginst1  
//
void debug_cfg_DMASEV
(
  uint8_t ns,
  uint8_t thread,      // 0- manager thread; 1- channel thread
  uint8_t ch_num,      // only use for channel thread
  uint8_t event_num
);


void
get_fault_info(void);

#define SAR (0x00)
#define CCR (0x01)
#define DAR (0x02)

//DMAMOV
uint8_t 
dma_mov_instruction
(
    uint8_t *paddr,   //address
    uint8_t rd,       //choose one 0f SAR/CCR/DAR 
    uint32_t value    //corresponding regsiter value
);

void 
manager_thread_init
(
    uint8_t ns,        //1:none-secure, 0:secure
    uint8_t channel,   //0~7:channel0~channel7
    uint32_t pc        //the addr of intruction to be executed
);

//NOTE:total num of data to transfer is data_size*data_num
void mem_to_mem
(
    uint8_t  endian_swap_size,//whether data can be swapped between little-endian (LE) and byte-invariant big-endian (BE-8) formats
    uint8_t  manager_state,//after exit from reset, config manager state 1(non-secure),0(secure state)
    uint32_t src,        //source address 
    uint32_t des,        //destination address
    uint32_t data_size,  //per burst size, unit:bytes
    uint32_t data_num,   //number of data to transfer
    uint8_t  channel,    //the channel to use for transfer
    int8_t   happen_event,//when dma complete, the event choosed happen.if no need, set as -1
    int8_t   wait_event  //until this event set, dma transfer begin.if no need, set as -1
);

//NOTE:total num of data to transfer is data_num (units:bytes)
//in this test case, source is Periph, destination is memory
void periph_to_mem
(
    uint8_t  manager_state,//after exit from reset, config manager state 1(non-secure),0(secure state)
    uint32_t src,        //source address 
    uint32_t des,        //destination address
    uint32_t data_size,  //per burst size, unit:bytes
    uint32_t data_num,   //number of bytes to transfer
    uint8_t  channel,    //the channel to use for transfer
    uint8_t  periph,     //peripheral number
    int8_t  happen_event //when dma complete, the event choosed happen.if no need, set as -1
);

//NOTE:total num of data to transfer is data_num (units:bytes)
//in this test case, source is memory, destination is Periph
void mem_to_periph
(
    uint8_t  manager_state,//after exit from reset, config manager state 1(non-secure),0(secure state)
    uint32_t src,        //source address 
    uint32_t des,        //destination address
    uint32_t data_size,  //per burst size, unit:bytes
    uint32_t data_num,   //number of bytes to transfer
    uint8_t  channel,    //the channel to use for transfer
    uint8_t  periph,     //peripheral number
    int8_t  happen_event //when dma complete, the event choosed happen.if no need, set as -1
);

//NOTE:transfered size of data is data_size*data_num
void acp_mem_to_mem
(
    uint8_t  manager_state,//after exit from reset, config manager state 1(non-secure),0(secure state)
    uint32_t src,        //source address
    uint32_t des,        //destination address
    uint32_t data_size,  //per burst size, unit:bytes
    uint32_t data_num,   //number of data to transfer
    uint8_t  channel,    //the channel to use for transfer
    int8_t   happen_event,//when dma complete, the event choosed happen.if no need, set as -1
    int8_t   wait_event  //until this event set, dma transfer begin.if no need, set as -1
);

//NOTE:total num of data to transfer is data_num (units:bytes)
//use DMA preload to transfer data
void mem_to_mem_preload
(
    uint8_t  manager_state,//after exit from reset, config manager state 1(non-secure),0(secure state)
    uint32_t src,        //source address
    uint32_t des,        //destination address
    uint32_t data_size,  //per burst size, unit:bytes
    uint32_t data_num,   //number of bytes to transfer
    uint8_t  channel,    //the channel to use for transfer
    int8_t   happen_event,//when dma complete, the event choosed happen.if no need, set as -1
    int8_t   wait_event  //until this event set, dma transfer begin.if no need, set as -1
);

uint32_t dma_getstate
(
    int manager,
    int channel_num
);

#define UNTIL(u, t, s) while(!(dma_getstate(u, t)&(s))) {printf("wait\n");};

#endif // __DMA_H__




