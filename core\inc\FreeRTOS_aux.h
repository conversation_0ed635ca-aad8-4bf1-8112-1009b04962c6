/* 用于FreeRTOS的辅助函数
    printTaskInfo                           主动调用——打印操作系统线程执行状况，需要创建一个任务，作为任务入口函数使用

    vApplicationStackOverflowHook           自动调用——栈溢出内容
    vApplicationGetIdleTaskMemory           自动调用——静态分配开启时，为IDLE任务分配静态的TCB和运行栈
    vApplicationGetTimerTaskMemory          自动调用——静态分配开启时，为系统定时器分配静态的TCB和运行栈
 */
#ifndef _FREERTOS_AUX_H
#define _FREERTOS_AUX_H

#include "FreeRTOS.h"
#include "task.h"

#ifdef __cplusplus
extern "C" {
#endif
/***************************************通用程序开始********************************************************/
/* 可视化当前系统中线程运行状态 */
#if (( configUSE_TRACE_FACILITY == 1 ) && ( configUSE_STATS_FORMATTING_FUNCTIONS == 1 ))
void getSysInfo(char *buffer);

/* 任务函数，每隔一秒打印出系统当前线程使用情况 */
void printTaskInfo();

#endif

/* 任务堆栈溢出后，会调用此处的钩子函数 */
#if (configCHECK_FOR_STACK_OVERFLOW > 0)
void vApplicationStackOverflowHook(TaskHandle_t xTask, char *pcTaskName);
#endif

/* 静态分配开启时，任务调度器创建IDLE任务时需要使用此函数分配IDLE任务的TCB空间和运行栈空间 */
#if (configSUPPORT_STATIC_ALLOCATION == 1)
void vApplicationGetIdleTaskMemory(StaticTask_t **ppxIdleTaskTCBBuffer,
                                   StackType_t **ppxIdleTaskStackBuffer,
                                   uint32_t *pulIdleTaskStackSize);
#endif

/* configSUPPORT_STATIC_ALLOCATION和configUSE_TIMERS开启时，需要此函数为定时器任务分配TCB空间和运行栈空间 */
#if (configSUPPORT_STATIC_ALLOCATION == 1 && configUSE_TIMERS == 1)
/* configSUPPORT_STATIC_ALLOCATION and configUSE_TIMERS are both set to 1, so the
application must provide an implementation of vApplicationGetTimerTaskMemory()
to provide the memory that is used by the Timer service task. */
void vApplicationGetTimerTaskMemory(StaticTask_t **ppxTimerTaskTCBBuffer,
                                    StackType_t **ppxTimerTaskStackBuffer,
                                    uint32_t *puxTimerTaskStackSize);
#endif

#ifdef __cplusplus
}
#endif
/***************************************通用程序结束********************************************************/

#endif