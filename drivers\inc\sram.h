#include "soc502_parameter.h"
#ifndef _SRAM_H
#define _SRAM_H

// SRAM 基地址
#define SRAM_BASE_ADDR EMIF_CS4_BASE
#define SRAM_SIZE_TEST 0x00000010

// AXI SRAM 基地址
#define AXI_SRAM_BASE_ADDR AXI_SRAM_BASE
#define AXI_SRAM_SIZE_TEST 0x00000010

// SRAM 函数定义
void sram_test(unsigned int base_addr, unsigned int sram_size);

void sram_test_32bit(unsigned int base_addr, unsigned int sram_size);
void sram_test_16bit(unsigned int base_addr, unsigned int sram_size);
void sram_test_8bit(unsigned int base_addr, unsigned int sram_size);

#endif /* _SRAM_H */
