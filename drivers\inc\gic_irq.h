/*
* Copyright (C) 2019 by Xi'an AeroSpace HuaXun Technology Co.,Ltd. All Rights Reserved
* MODULE:   GIC
* FILENAME: GIC_IRQ.H
* Author   :<EMAIL>
* Date:	   2019.05.05
*/

#ifndef _GIC_IRQ_H
#define _GIC_IRQ_H

/***************************************************************************************
*   INCLUDES
***************************************************************************************/
//#include "stdtype.h"
//#include "public.h"

/***************************************************************************************
*   DEFINES
***************************************************************************************/
#define PERIPH_BASE			0x3FFF0000
#define GICD_BASE			(PERIPH_BASE + 0x1000)
#define GICC_BASE			(PERIPH_BASE + 0x100)


#define GIC_CPU_CTRL			(GICC_BASE + 0x00)
#define GIC_CPU_PRIMASK			(GICC_BASE + 0x04)
#define GIC_CPU_BINPOINT		(GICC_BASE + 0x08)
#define GIC_CPU_INTACK			(GICC_BASE + 0x0c)
#define GIC_CPU_EOI			(GICC_BASE + 0x10)
#define GIC_CPU_RUNNINGPRI		(GICC_BASE + 0x14)
#define GIC_CPU_HIGHPRI			(GICC_BASE + 0x18)
#define GIC_CPU_IID			(GICC_BASE + 0xFC)

#define GIC_CPU_ENABLE			0x1
#define GIC_CPU_DISABLE			0x0


#define GIC_DIST_CTRL			(GICD_BASE + 0x000)
#define GIC_DIST_CTR			(GICD_BASE + 0x004)
#define GIC_DIST_IID			(GICD_BASE + 0x008)
#define GIC_DIST_IGROUP			(GICD_BASE + 0x080)
#define GIC_DIST_ENABLE_SET		(GICD_BASE + 0x100)
#define GIC_DIST_ENABLE_CLEAR		(GICD_BASE + 0x180)
#define GIC_DIST_PENDING_SET		(GICD_BASE + 0x200)
#define GIC_DIST_PENDING_CLEAR		(GICD_BASE + 0x280)
#define GIC_DIST_ACTIVE_SET		(GICD_BASE + 0x300)
#define GIC_DIST_ACTIVE_CLEAR		(GICD_BASE + 0x380)
#define GIC_DIST_PRI			(GICD_BASE + 0x400)
#define GIC_DIST_TARGET			(GICD_BASE + 0x800)
#define GIC_DIST_CONFIG			(GICD_BASE + 0xc00)
#define GIC_DIST_SOFTINT		(GICD_BASE + 0xf00)

#define GIC_DIST_ENABLE			0x3
#define GIC_DIST_DISABLE		0x0


#define IRQ_OFFSET			32

#define GIS_IRQ_GLOBAL_TIMER          27

//
// Interrupt sources
//
#define GIS_NMI_SYS_NMI0        0
#define GIS_IRQ_PMU             0
#define GIS_NMI_SYS_NMI1        1
#define GIS_IRQ_NAND            1
#define GIS_IRQ_SYSC            2
#define GIS_IRQ_USB2            3
#define GIS_IRQ_USB3            4
#define GIS_IRQ_HDMAC           5
#define GIS_IRQ_GPIO            6
#define GIS_IRQ_QSPI            7
#define GIS_IRQ_PCIE            8
#define GIS_IRQ_GPU             9
#define GIS_IRQ_VCODEC         10
#define GIS_IRQ_DISP_DE0       11
#define GIS_IRQ_GMAC0          12
#define GIS_IRQ_GMAC1          13
#define GIS_IRQ_GMAC2          14
#define GIS_IRQ_SDIO           15
#define GIS_IRQ_RAPIDIO        16
#define GIS_IRQ_JESD204B       17
#define GIS_IRQ_WDT0           18
#define GIS_IRQ_WDT1           19
#define GIS_IRQ_SPI0           20
#define GIS_IRQ_SPI1           21
#define GIS_IRQ_SPI2           22
#define GIS_IRQ_SPI3           23
#define GIS_IRQ_UART0          24
#define GIS_IRQ_UART1          25
#define GIS_IRQ_UART2          26
#define GIS_IRQ_UART3          27
#define GIS_IRQ_UART4          28
#define GIS_IRQ_I2C0           29
#define GIS_IRQ_I2C1           30
#define GIS_IRQ_I2C2           31
#define GIS_IRQ_I2C3           32
#define GIS_IRQ_DDRC           33
#define GIS_IRQ_TIMER0         34
#define GIS_IRQ_MIPI_INTR1     35
#define GIS_IRQ_EMEM           36
#define GIS_IRQ_CAN0           37
#define GIS_IRQ_CAN1           38
#define GIS_IRQ_CAN2           39
#define GIS_IRQ_CAN3           40
#define GIS_IRQ_I2SRX          41
#define GIS_IRQ_I2STX          42
#define GIS_IRQ_ISRAM          43
#define GIS_IRQ_USB2_ID        44
#define GIS_IRQ_USB2_VBUS_POFF 45
#define GIS_IRQ_USB2_VBUS_PON  46
#define GIS_IRQ_USB3_ID        47
#define GIS_IRQ_USB3_VBUS_POFF 48
#define GIS_IRQ_USB3_VBUS_PON  49
#define GIS_IRQ_MCBSP_TX       50
#define GIS_IRQ_PCIEPHY        51
#define GIS_IRQ_DDRPHY         52
#define GIS_IRQ_MIPITX         53
#define GIS_IRQ_LVDSRX         54
#define GIS_IRQ_GMAC0_LPI      55
#define GIS_IRQ_GMAC0_PMT      56
#define GIS_IRQ_GMAC1_LPI      57
#define GIS_IRQ_GMAC1_PMT      58
#define GIS_IRQ_GMAC2_LPI      59
#define GIS_IRQ_GMAC2_PMT      60
#define GIS_IRQ_GPU_INRPP1     61
#define GIS_IRQ_GPU_INRPP2     62
#define GIS_IRQ_GPU_INRPP3     63
#define GIS_IRQ_GPU_INRPP4     64
#define GIS_IRQ_GPU_INRPP5     65
#define GIS_IRQ_GPU_INRPP6     66
#define GIS_IRQ_GPU_INRPP7     67
#define GIS_IRQ_GPU_IRQGPMMU   68
#define GIS_IRQ_GPU_IRQPMU     69
#define GIS_IRQ_GPU_IRQPP0     70
#define GIS_IRQ_GPU_IRQPPMMU0  71
#define GIS_IRQ_GPU_IRQPPMMU1  72
#define GIS_IRQ_GPU_IRQPPMMU2  73
#define GIS_IRQ_GPU_IRQPPMMU3  74
#define GIS_IRQ_GPU_IRQPPMMU4  75
#define GIS_IRQ_GPU_IRQPPMMU5  76
#define GIS_IRQ_GPU_IRQPPMMU6  77
#define GIS_IRQ_GPU_IRQPPMMU7  78
#define GIS_IRQ_DFI_ALERT_ERR  79
#define GIS_IRQ_DDRC_ECC_CORRECTED_ERR 80
#define GIS_IRQ_DDRC_ECC_UNCORRECTED_ERR 81
#define GIS_IRQ_ARPOISON_INTR_1 82
#define GIS_IRQ_ARPOISON_INTR_2 83
#define GIS_IRQ_ARPOISON_INTR_3 84
#define GIS_IRQ_ARPOISON_INTR_4 85
#define GIS_IRQ_ARPOISON_INTR_5 86
#define GIS_IRQ_ARPOISON_INTR_6 87
#define GIS_IRQ_ARPOISON_INTR_7 88
#define GIS_IRQ_ARPOISON_INTR_8 89
#define GIS_IRQ_ARPOISON_INTR_9 90
#define GIS_IRQ_ARPOISON_INTR_10 91
#define GIS_IRQ_ARPOISON_INTR_11 92
#define GIS_IRQ_ARPOISON_INTR_12 93
#define GIS_IRQ_ARPOISON_INTR_13 94
#define GIS_IRQ_ARPOISON_INTR_14 95
#define GIS_IRQ_ARPOISON_INTR_15 96
#define GIS_IRQ_AWPOISON_INTR_1 97
#define GIS_IRQ_AWPOISON_INTR_2 98
#define GIS_IRQ_AWPOISON_INTR_3 99
#define GIS_IRQ_AWPOISON_INTR_4 100
#define GIS_IRQ_AWPOISON_INTR_5 101
#define GIS_IRQ_AWPOISON_INTR_6 102
#define GIS_IRQ_AWPOISON_INTR_7 103
#define GIS_IRQ_AWPOISON_INTR_8 104
#define GIS_IRQ_AWPOISON_INTR_9 105
#define GIS_IRQ_AWPOISON_INTR_10 106
#define GIS_IRQ_AWPOISON_INTR_11 107
#define GIS_IRQ_AWPOISON_INTR_12 108
#define GIS_IRQ_AWPOISON_INTR_13 109
#define GIS_IRQ_AWPOISON_INTR_14 110
#define GIS_IRQ_AWPOISON_INTR_15 111
#define GIS_IRQ_TIMER0_INTR1   112
#define GIS_IRQ_TIMER0_INTR2   113
#define GIS_IRQ_TIMER1         114
#define GIS_IRQ_TIMER1_INTR1   115
#define GIS_IRQ_TIMER1_INTR2   116
#define GIS_IRQ_TIMER2         117
#define GIS_IRQ_TIMER2_INTR1   118
#define GIS_IRQ_TIMER2_INTR2   119
#define GIS_IRQ_TIMER3         120
#define GIS_IRQ_TIMER3_INTR1   121
#define GIS_IRQ_TIMER3_INTR2   122
#define GIS_IRQ_MIPI_INTR2     123
#define GIS_IRQ_DISP_SE0       124
#define GIS_IRQ_DISP_DE1       125
#define GIS_IRQ_DISP_SE1       126
#define GIS_IRQ_MCBSP_RX       127
#define GIS_IRQ_HDMAC2         128
#define GIS_IRQ_ADMAC          129
#define GIS_IRQ_I2C4           130

/***************************************************************************************
*   ENUM
***************************************************************************************/
/* IRQ trigger mode */
enum irq_trig_mode
{
	IRQ_TRIG_LEVEL = 0, //high level triggered
	IRQ_TRIG_EDGE,	// rising edge triggered
};

/* IRQ target CPU */
enum irq_target
{
	IRQ_TGT_CPU0 = 0,
	IRQ_TGT_CPU1,
};


/***************************************************************************************
*   FUNCTION
***************************************************************************************/
void GIC_IRQ_Disable_Dist(void);
void GIC_IRQ_Disable_Cpu(void);
void GIC_IRQ_Disable_Interrupts(void);
void GIC_IRQ_Clear_Pending(unsigned int irq);
void GIC_IRQ_Clear_Active(unsigned int irq);
void GIC_IRQ_Set_Trigger(unsigned int irq, unsigned int mode);
void GIC_IRQ_Set_Target(unsigned int irq, unsigned int cpu);
void GIC_IRQ_Set_Priority(unsigned int irq, unsigned int prio);
void GIC_IRQ_Set_Primask(unsigned int mask);
void GIC_IRQ_Enable_Dist(void);
void GIC_IRQ_Enable_Cpu(void);
void GIC_Enable_Interrupts(void);
void GIC_IRQ_unmask(unsigned int irq);
void GIC_IRQ_Enable_Interrupts(void);
void GIC_IRQ_Init(unsigned int Irq);
unsigned int GIC_IRQ_GetINTACK(void);
void GIC_IRQ_SetEOI(unsigned int irq);

#endif
