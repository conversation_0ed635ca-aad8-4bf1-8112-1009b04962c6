#ifndef __SYSCTL_H__
#define __SYSCTL_H__

#include "common.h"

typedef struct 
{
	__IO uint32_t FilterEn;              //0x000
	__IO uint32_t FilterStart;           //0x004
	__IO uint32_t FilterEnd;             //0x008
	__IO uint32_t RESERVED0;             //0x00C
	__IO uint32_t MAXClkLatency;         //0x010
	__IO uint32_t RESERVED1[187];        //0x200~0x3FC
	__IO uint32_t MPU_CLK_OFF_POSE_CNT;  //0x300
	__IO uint32_t A9_RSTN_POSE_CNT;      //0x304
	__IO uint32_t MPU_CLK_OFF_NEGE_CNT;  //0x308
	__IO uint32_t DEBUG_RST_CNT;         //0x30C
	__IO uint32_t BUS_RST_CNT;           //0x310
	__IO uint32_t IPS_RST_CNT;           //0x314
	__IO uint32_t DDR_POWER_OFF_CNT;     //0x318
	__IO uint32_t DDR_OUTSTANDING_CNT;   //0x31C
	__IO uint32_t RESERVED2[56];        
	__IO uint32_t DDR_LOW_POWER_SYSREQ;  //0x400
	__IO uint32_t DDR_LOW_POWER_SYSACK;  //0x404
	__IO uint32_t DDR_LOW_POWER_ACTIVE;  //0x408
	__IO uint32_t DDR_LOW_POWER_CTL_STAT;//0x40C
	__IO uint32_t RESERVED3[60];
	__IO uint32_t DDR_RST_FLAG;          //0x500 
	__IO uint32_t RESERVED4[64];
	__IO uint32_t CHIP_RST_HAPPENED;     //0x604
	__IO uint32_t SW_RST_HAPPENED;       //0x608
	__IO uint32_t CPU_WDG_RST_HAPPENED;  //0x60C
	__IO uint32_t SYS_WDG_RST_HAPPENED;  //0x610
	__IO uint32_t RESERVED5[123];
	__IO uint32_t QSPI_SS_IN_N;          //0x800
	__IO uint32_t SPI_UB_SS_IN_N;        //0x804
	__IO uint32_t SPI_SS_IN_N;           //0x808
	__IO uint32_t RESERVED6[61];
	__IO uint32_t A9_DBGEN;              //0x900
	__IO uint32_t A9_SPIDEN;             //0x904
	__IO uint32_t A9_NIDEN;              //0x908
	__IO uint32_t A9_SPNIDEN;            //0x90C
	__IO uint32_t RESERVED7[60];
	__IO uint32_t B1553_REG;             //0xA00
	__IO uint32_t RESERVED8[63];
	__IO uint32_t DDR_PORT_REG;          //0xB00
	__IO uint32_t RESERVED9[191];
	__IO uint32_t TM_REG;                //0xE00
	__IO uint32_t AUX_REG;               //0xE04
	__IO uint32_t TUBE_REG;              //0xE08
	__IO uint32_t DDR_LP_ENABLE;         //0xE0C
	__IO uint32_t BOOT_SELECT;           //0xE10
	__IO uint32_t RESERVED10[59];
	__IO uint32_t NVREG0;                //0xF00
	__IO uint32_t NVREG1;                //0xF04
	__IO uint32_t NVREG2;                //0xF08
	__IO uint32_t NVREG3;                //0xF0C
	__IO uint32_t NVREG4;                //0xF10
	__IO uint32_t NVREG5;                //0xF14
	__IO uint32_t NVREG6;                //0xF18
	__IO uint32_t NVREG7;                //0xF1C
	__IO uint32_t NVREG8;                //0xF20
	__IO uint32_t NVREG9;                //0xF24
	__IO uint32_t NVREG10;               //0xF28
	__IO uint32_t NVREG11;               //0xF2C
	__IO uint32_t NVREG12;               //0xF30
	__IO uint32_t NVREG13;               //0xF34
	__IO uint32_t NVREG14;               //0xF38
	__IO uint32_t NVREG15;               //0xF3C
} SYS_CTL_TypeDef;

#define SYS_CTL_REG     ((SYS_CTL_TypeDef*)SYS_CTL_BASE)

/*get boot select option*/
uint8_t get_boot_flag(void);

void spi_boot_cs_op(uint8_t val);

void spi_boot_wp_op(uint8_t val);

void spi_boot_hold_op(uint8_t val);
#endif

