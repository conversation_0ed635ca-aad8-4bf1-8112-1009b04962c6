#ifndef __INTERRUPT_H__
#define __INTERRUPT_H__

#include "common.h"

#define  N140_GIO_IRQ9              140        // ID140, level, gio_irq9
#define  N139_GIO_IRQ8              139        // ID139, level, gio_irq8
#define  N138_GIO_IRQ7              138        // ID138, level, gio_irq7
#define  N137_GIO_IRQ6              137        // ID137, level, gio_irq6
#define  N136_GIO_IRQ5              136        // ID136, level, gio_irq5
#define  N135_GIO_IRQ4              135        // ID135, level, gio_irq4
#define  N134_GIO_IRQ3              134        // ID134, level, gio_irq3
#define  N133_GIO_IRQ2              133        // ID133, level, gio_irq2
#define  N132_GIO_IRQ1              132        // ID132, level, gio_irq1
#define  N131_GIO_IRQ0              131        // ID131, level, gio_irq0
#define  N130_I2C_IRQ1              130        // ID130, level, i2c_irq1
#define  N129_I2C_IRQ0              129        // ID129, level, i2c_irq0
#define  N128_SYS_WDG_IRQ1          128        // ID128, level, sys_wdg_irq1
#define  N127_SYS_WDG_IRQ0          127        // ID127, level, sys_wdg_irq0
#define  N126_TMR_IRQ3              126        // ID126, level, tmr_irq3
#define  N125_TMR_IRQ2              125        // ID125, level, tmr_irq2
#define  N124_TMR_IRQ1              124        // ID124, level, tmr_irq1
#define  N123_TMR_IRQ0              123        // ID123, level, tmr_irq0
#define  N122_PWM_IRQ3              122        // ID122, level, pwm_irq3
#define  N121_PWM_IRQ2              121        // ID121, level, pwm_irq2
#define  N120_PWM_IRQ1              120        // ID120, level, pwm_irq1
#define  N119_PWM_IRQ0              119        // ID119, level, pwm_irq0
#define  N118_SPI_IRQ3              118        // ID118, level, spi_irq3
#define  N117_SPI_IRQ2              117        // ID117, level, spi_irq2
#define  N116_SPI_IRQ1              116        // ID116, level, spi_irq1
#define  N115_SPI_IRQ0              115        // ID115, level, spi_irq0
#define  N114_SPI_UB_IRQ            114        // ID114, level, spi_ub_irq
#define  N113_QSPI_IRQ              113        // ID113, level, qspi_irq
#define  N112_UART_IRQ11            112        // ID112, level, uart_irq11
#define  N111_UART_IRQ10            111        // ID111, level, uart_irq10
#define  N110_UART_IRQ9             110        // ID110, level, uart_irq9
#define  N109_UART_IRQ8             109        // ID109, level, uart_irq8
#define  N108_UART_IRQ7             108        // ID108, level, uart_irq7
#define  N107_UART_IRQ6             107        // ID107, level, uart_irq6
#define  N106_UART_IRQ5             106        // ID106, level, uart_irq5
#define  N105_UART_IRQ4             105        // ID105, level, uart_irq4
#define  N104_UART_IRQ3             104        // ID104, level, uart_irq3
#define  N103_UART_IRQ2             103        // ID103, level, uart_irq2
#define  N102_UART_IRQ1             102        // ID102, level, uart_irq1
#define  N101_UART_IRQ0             101        // ID101, level, uart_irq0
#define  N100_PCA_IRQ5              100        // ID100, level, pca_irq5
#define  N99_PCA_IRQ4               99         // ID99,  level, pca_irq4
#define  N98_PCA_IRQ3               98         // ID98,  level, pca_irq3
#define  N97_PCA_IRQ2               97         // ID97,  level, pca_irq2
#define  N96_PCA_IRQ1               96         // ID96,  level, pca_irq1
#define  N95_PCA_IRQ0               95         // ID95,  level, pca_irq0
#define  N94_CAN_IRQ3               94         // ID94,  level, can_irq3
#define  N93_CAN_IRQ2               93         // ID93,  level, can_irq2
#define  N92_CAN_IRQ1               92         // ID92,  level, can_irq1
#define  N91_CAN_IRQ0               91         // ID91,  level, can_irq0
#define  N90_B1553_IRQ_M1           90         // ID90,  level, b1553_irq_m1
#define  N89_B1553_IRQ_M0           89         // ID89,  level, b1553_irq_m0
#define  N88_B1553_IRQ_H1           88         // ID88,  level, b1553_irq_h1
#define  N87_B1553_IRQ_H0           87         // ID87,  level, b1553_irq_h0
#define  N86_MAC_IRQ                86         // ID86,  level, mac_irq             
#define  N85_DDR_IRQ                85         // ID85,  level, ddr_irq
#define  N84_DMA_IRQ_ABORT          84         // ID84,  level, dma_irq_abort
#define  N83_DMA_IRQ15              83         // ID83,  level, dma_irq15            
#define  N82_DMA_IRQ14              82         // ID82,  level, dma_irq14            
#define  N81_DMA_IRQ13              81         // ID81,  level, dma_irq13            
#define  N80_DMA_IRQ12              80         // ID80,  level, dma_irq12            
#define  N79_DMA_IRQ11              79         // ID79,  level, dma_irq11            
#define  N78_DMA_IRQ10              78         // ID78,  level, dma_irq10            
#define  N77_DMA_IRQ9               77         // ID77,  level, dma_irq9            
#define  N76_DMA_IRQ8               76         // ID76,  level, dma_irq8            
#define  N75_DMA_IRQ7               75         // ID75,  level, dma_irq7            
#define  N74_DMA_IRQ6               74         // ID74,  level, dma_irq6            
#define  N73_DMA_IRQ5               73         // ID73,  level, dma_irq5            
#define  N72_DMA_IRQ4               72         // ID72,  level, dma_irq4            
#define  N71_DMA_IRQ3               71         // ID71,  level, dma_irq3            
#define  N70_DMA_IRQ2               70         // ID70,  level, dma_irq2            
#define  N69_DMA_IRQ1               69         // ID69,  level, dma_irq1            
#define  N68_DMA_IRQ0               68         // ID68,  level, dma_irq0            
#define  N67_L2C_IRQ                67         // ID67,  level, l2_irq
#define  N66_SCU_EV_ABORT_IRQ       66         // ID66,  edge, scu_ev_abort
#define  N65_SCU_PARITYFAIL1_IRQ    65         // ID65,  edge, scu_parityfail1
#define  N64_SCU_PARITYFAIL0_IRQ    64         // ID64,  edge, scu_parityfail0
#define  N63_CPU1_DEFLAGS6_IRQ      63         // ID63,  level, cpu1_deflags6
#define  N62_CPU1_DEFLAGS5_IRQ      62         // ID62,  level, cpu1_deflags5
#define  N61_CPU1_DEFLAGS4_IRQ      61         // ID61,  level, cpu1_deflags4
#define  N60_CPU1_DEFLAGS3_IRQ      60         // ID60,  level, cpu1_deflags3
#define  N59_CPU1_DEFLAGS2_IRQ      59         // ID59,  level, cpu1_deflags2
#define  N58_CPU1_DEFLAGS1_IRQ      58         // ID58,  level, cpu1_deflags1
#define  N57_CPU1_DEFLAGS0_IRQ      57         // ID57,  level, cpu1_deflags0
#define  N56_CPU1_PARITYFAIL0_IRQ   56         // ID56,  edge, cpu1_parityfail_D_Data
#define  N55_CPU1_PARITYFAIL1_IRQ   55         // ID55,  edge, cpu1_parityfail_D_Tag 
#define  N54_CPU1_PARITYFAIL2_IRQ   54         // ID54,  edge, cpu1_parityfail_D_Oute
#define  N53_CPU1_PARITYFAIL3_IRQ   53         // ID53,  edge, cpu1_parityfail_TLB   
#define  N52_CPU1_PARITYFAIL4_IRQ   52         // ID52,  edge, cpu1_parityfail_I_Data
#define  N51_CPU1_PARITYFAIL5_IRQ   51         // ID51,  edge, cpu1_parityfail_I_Tag
#define  N50_CPU1_PARITYFAIL6_IRQ   50         // ID50,  edge, cpu1_parityfail_GHB 
#define  N49_CPU1_PARITYFAIL7_IRQ   49         // ID49,  edge, cpu1_parityfail_BTAC
#define  N48_CPU1_PARITYFAIL_IRQ    48         // ID48,  edge, cpu1_parityfail
#define  N47_CPU0_DEFLAGS6_IRQ      47         // ID47,  level, cpu0_deflags6
#define  N46_CPU0_DEFLAGS5_IRQ      46         // ID46,  level, cpu0_deflags5
#define  N45_CPU0_DEFLAGS4_IRQ      45         // ID45,  level, cpu0_deflags4
#define  N44_CPU0_DEFLAGS3_IRQ      44         // ID44,  level, cpu0_deflags3
#define  N43_CPU0_DEFLAGS2_IRQ      43         // ID43,  level, cpu0_deflags2
#define  N42_CPU0_DEFLAGS1_IRQ      42         // ID42,  level, cpu0_deflags1
#define  N41_CPU0_DEFLAGS0_IRQ      41         // ID41,  level, cpu0_deflags0
#define  N40_CPU0_PARITYFAIL0_IRQ   40         // ID40,  edge, cpu0_parityfail_D_Data
#define  N39_CPU0_PARITYFAIL1_IRQ   39         // ID39,  edge, cpu0_parityfail_D_Tag 
#define  N38_CPU0_PARITYFAIL2_IRQ   38         // ID38,  edge, cpu0_parityfail_D_Oute
#define  N37_CPU0_PARITYFAIL3_IRQ   37         // ID37,  edge, cpu0_parityfail_TLB   
#define  N36_CPU0_PARITYFAIL4_IRQ   36         // ID36,  edge, cpu0_parityfail_I_Data
#define  N35_CPU0_PARITYFAIL5_IRQ   35         // ID35,  edge, cpu0_parityfail_I_Tag
#define  N34_CPU0_PARITYFAIL6_IRQ   34         // ID34,  edge, cpu0_parityfail_GHB 
#define  N33_CPU0_PARITYFAIL7_IRQ   33         // ID33,  edge, cpu0_parityfail_BTAC
#define  N32_CPU0_PARITYFAIL_IRQ    32         // ID32,  edge, cpu0_parityfail
#define  N30_WATCHDOG_IRQ           30         // ID30,  edge, watch dog 
#define  N29_PRIVATE_TIMER_IRQ      29         // ID29,  edge, private timer
#define  N27_GLOBAL_TIMER_IRQ       27         // ID27,  edge, global timer

void irqs_handler(uint32_t irq_id);

//-----------------------------
// Include peripheral irq handler
//
void handler_n140_gio_irq9(void);
void handler_n139_gio_irq8(void);
void handler_n138_gio_irq7(void);
void handler_n137_gio_irq6(void);
void handler_n136_gio_irq5(void);
void handler_n135_gio_irq4(void);
void handler_n134_gio_irq3(void);
void handler_n133_gio_irq2(void);
void handler_n132_gio_irq1(void);
void handler_n131_gio_irq0(void);
void handler_n130_i2c_irq1(void);
void handler_n129_i2c_irq0(void);
void handler_n128_sys_wdg_irq1(void);
void handler_n127_sys_wdg_irq0(void);
void handler_n126_tmr_irq3(void);
void handler_n125_tmr_irq2(void);
void handler_n124_tmr_irq1(void);
void handler_n123_tmr_irq0(void);
void handler_n122_pwm_irq3(void);
void handler_n121_pwm_irq2(void);
void handler_n120_pwm_irq1(void);
void handler_n119_pwm_irq0(void);
void handler_n118_spi_irq3(void);
void handler_n117_spi_irq2(void);
void handler_n116_spi_irq1(void);
void handler_n115_spi_irq0(void);
void handler_n114_spi_ub_irq(void);
void handler_n113_qspi_irq(void);
void handler_n112_uart_irq11(void);
void handler_n111_uart_irq10(void);
void handler_n110_uart_irq9(void); 
void handler_n109_uart_irq8(void);
void handler_n108_uart_irq7(void);
void handler_n107_uart_irq6(void);
void handler_n106_uart_irq5(void);
void handler_n105_uart_irq4(void);
void handler_n104_uart_irq3(void);
void handler_n103_uart_irq2(void);
void handler_n102_uart_irq1(void);
void handler_n101_uart_irq0(void);
void handler_n100_pca_irq5(void);
void handler_n99_pca_irq4(void); 
void handler_n98_pca_irq3(void); 
void handler_n97_pca_irq2(void); 
void handler_n96_pca_irq1(void); 
void handler_n95_pca_irq0(void); 
void handler_n94_can_irq3(void); 
void handler_n93_can_irq2(void); 
void handler_n92_can_irq1(void); 
void handler_n91_can_irq0(void); 
void handler_n90_b1553_irq_m1(void);
void handler_n89_b1553_irq_m0(void);
void handler_n88_b1553_irq_h1(void);
void handler_n87_b1553_irq_h0(void);
void handler_n86_mac_irq(void);
void handler_n85_ddr_irq(void);  
void handler_n84_dma_irq_abort(void);
void handler_n83_dma_irq15(void);
void handler_n82_dma_irq14(void);
void handler_n81_dma_irq13(void);
void handler_n80_dma_irq12(void);
void handler_n79_dma_irq11(void);
void handler_n78_dma_irq10(void);
void handler_n77_dma_irq9(void); 
void handler_n76_dma_irq8(void); 
void handler_n75_dma_irq7(void); 
void handler_n74_dma_irq6(void); 
void handler_n73_dma_irq5(void); 
void handler_n72_dma_irq4(void); 
void handler_n71_dma_irq3(void); 
void handler_n70_dma_irq2(void); 
void handler_n69_dma_irq1(void); 
void handler_n68_dma_irq0(void); 
void handler_n67_l2c_irq(void); 
void handler_n66_scu_ev_abort_irq(void);    
void handler_n65_scu_parityfail1_irq(void); 
void handler_n64_scu_parityfail0_irq(void); 
void handler_n63_cpu1_deflags6_irq(void);   
void handler_n62_cpu1_deflags5_irq(void);   
void handler_n61_cpu1_deflags4_irq(void);   
void handler_n60_cpu1_deflags3_irq(void);   
void handler_n59_cpu1_deflags2_irq(void);   
void handler_n58_cpu1_deflags1_irq(void);   
void handler_n57_cpu1_deflags0_irq(void);   
void handler_n56_cpu1_parityfail0_irq(void);
void handler_n55_cpu1_parityfail1_irq(void);
void handler_n54_cpu1_parityfail2_irq(void);
void handler_n53_cpu1_parityfail3_irq(void);
void handler_n52_cpu1_parityfail4_irq(void);
void handler_n51_cpu1_parityfail5_irq(void);
void handler_n50_cpu1_parityfail6_irq(void);
void handler_n49_cpu1_parityfail7_irq(void);
void handler_n48_cpu1_parityfail_irq(void); 
void handler_n47_cpu0_deflags6_irq(void);   
void handler_n46_cpu0_deflags5_irq(void);   
void handler_n45_cpu0_deflags4_irq(void);   
void handler_n44_cpu0_deflags3_irq(void);   
void handler_n43_cpu0_deflags2_irq(void);   
void handler_n42_cpu0_deflags1_irq(void);   
void handler_n41_cpu0_deflags0_irq(void);   
void handler_n40_cpu0_parityfail0_irq(void);
void handler_n39_cpu0_parityfail1_irq(void);
void handler_n38_cpu0_parityfail2_irq(void);
void handler_n37_cpu0_parityfail3_irq(void);
void handler_n36_cpu0_parityfail4_irq(void);
void handler_n35_cpu0_parityfail5_irq(void);
void handler_n34_cpu0_parityfail6_irq(void);
void handler_n33_cpu0_parityfail7_irq(void);
void handler_n32_cpu0_parityfail_irq(void);
void handler_n30_watchdog_irq(void);     
void handler_n29_private_timer_irq(void);
void handler_n27_global_timer_irq(void); 
#endif



