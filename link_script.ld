
/**
 * <PERSON>er script file for Cortex-A9 FreeRTOS project
 */

/* Memory layout for the target system */
MEMORY
{
    RAM (rwx) : ORIGIN = 0x40018000, LENGTH = 0x8000000  /* 128MB RAM starting at 0x40018000 */
}

/* Entry point */
ENTRY(_start)

SECTIONS
{
    /* Vector table and code section */
    .text : ALIGN(4)
    {
        __text_start__ = .;
        obj/vectors.o (.vectors);
        *(.text*)
        *(.glue_7)
        *(.glue_7t)
        *(.vfp11_veneer)
        *(.v4_bx)
        __text_end__ = .;
    } > RAM

    /* Read-only data section */
    .rodata : ALIGN(4)
    {
        __rodata_start__ = .;
        *(.rodata*)
        __rodata_end__ = .;
    } > RAM

    /* Initialized data section */
    .data : ALIGN(4)
    {
        __data_start__ = .;
        *(.data*)
        __data_end__ = .;
    } > RAM

    /* Test case section */
    _case_list_start = .;
    .test_case : ALIGN(4)
    {
        *(.test_case)
    } > RAM
    _case_list_end = .;

    /* Uninitialized data section */
    .bss : ALIGN(4)
    {
        __bss_start__ = .;
        *(.bss*)
        *(COMMON)
        __bss_end__ = .;
    } > RAM

    /* Heap section */
    .heap : ALIGN(4)
    {
        __heap_start__ = .;
        __end__ = .;
        PROVIDE(end = .);
        *(.heap*)
        . = . + 0x100000;  /* Reserve 1MB for heap */
        __heap_end__ = .;
        __HeapLimit = __heap_end__;
    } > RAM

    /* Stack section */
    .stack : ALIGN(8)
    {
        __stack_start__ = .;
        . = . + 0x10000;  /* Reserve 64KB for stack */
        __stack_end__ = .;
        __StackTop = __stack_end__;
    } > RAM

    /* Debug sections */
    .debug_info     0 : { *(.debug_info) }
    .debug_abbrev   0 : { *(.debug_abbrev) }
    .debug_line     0 : { *(.debug_line) }
    .debug_frame    0 : { *(.debug_frame) }
    .debug_str      0 : { *(.debug_str) }
    .debug_loc      0 : { *(.debug_loc) }
    .debug_aranges  0 : { *(.debug_aranges) }
    .debug_ranges   0 : { *(.debug_ranges) }
    .debug_macinfo  0 : { *(.debug_macinfo) }
    .comment        0 : { *(.comment) }
    .ARM.attributes 0 : { *(.ARM.attributes) }
}

