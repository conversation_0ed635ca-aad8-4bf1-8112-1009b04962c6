/*
 * armboot - Startup Code for OMAP3530/ARM Cortex CPU-core
 *
 * Copyright (c) 2004	Texas Instruments <<EMAIL>>
 *
 * Copyright (c) 2001	<PERSON> <<EMAIL>>
 * Copyright (c) 2002	<PERSON> <<EMAIL>>
 * Copyright (c) 2002	<PERSON> <<EMAIL>>
 * Copyright (c) 2003	<PERSON> <<EMAIL>>
 * Copyright (c) 2003	Kshitij <<EMAIL>>
 * Copyright (c) 2006-2008 <PERSON> <<EMAIL>>
 *
 * SPDX-License-Identifier:	GPL-2.0+
 */

#include "system.h"
#include "linkage.h"

#define OUT_TUBE
//#define DCache_on

/*************************************************************************
 *
 * Startup Code (reset vector)
 *
 * do important init only if we don't start from memory!
 * setup Memory and board specific bits prior to relocation.
 * relocate armboot to ram
 * setup stack
 *
 *************************************************************************/

#define TUBE_BASE    0x4fff0000
#define UART_BASE    0xA0000100
#define LOCALBUS_REG 0xA00A0000
//#define STACK_TOP    (0x14800000)
#define STACK_TOP    (0x400fFFF0)
//#define STACK_TOP    (0xC00fFFF0)

#define WRMEM_WS     (8 << 0)
#define WRMEM_BS     (13 << 8)
#define WRMEM_RMW    (0 << 12)
#define WR_BITSIZE     (2 << 14)
#define ORMEM_WS     (128 << 0)
#define ORMEM_BS     (15 << 8)
#define OR_RMW_EN     (0 << 12)
#define OR_PG_EN     (1 << 16)
#define OR_BITSIZE     (0 << 14)
#define IO_WS        (128 << 0)
#define IO_WT        (2 << 8)
#define IO_RDY       (0 << 10)
#define IO_DIVD      (0 << 11)
#define IO_FILTER    (3 << 13)
#define OR_SETHOLD	 (0 << 0)

	.globl   lowlevel_init
	.globl   readl
	.globl   uart_out
	.globl   newline
	.globl   puts


/* r8 ~ r12: reserved for function call */


.macro rd_lbus tmp
	ldr      r10, =LOCALBUS_REG
	add      r10, r10, \tmp
	
	mov      r8, r10
	bl       putnum        // print the addrss

	ldr      r8, =echo_equal
	bl       puts          // print " = "

	ldr r8, [r10]
	bl       putnum        // print the vale
	bl       newline       // print enter
.endm

//add by LXG begin
#ifdef DCache_on
.macro ARM_SECTION_ENTRY base,tex,ap,d,c,b
	.word (\base << 20) | (\tex << 12)| (\ap << 10) | \
          (\d << 5) | (0<<4) | (\c << 3) | (\b << 2) | (1<<1)
.endm


.section .mmudata, "a"
	.align 14
	// the following alignment creates the mmu table at address 0x4000.
	.globl mmu_table
	//ARM_SECTION_ENTRY _base,0,3,0,0,0 //strong order
	//ARM_SECTION_ENTRY _base,0,3,0,1,0 //wite through no Write-Allocate
	//ARM_SECTION_ENTRY _base,0,3,0,1,1 //wite back no Write-Allocate
	//ARM_SECTION_ENTRY _base,1,3,0,1,1 //wite back Write-Allocate
mmu_table: 
	.set _base,0
	 // 1:1 mapping for debugging
	.rept 0x40   //RO_1 
	 ARM_SECTION_ENTRY _base,0,3,0,1,0 //Outer and Inner Write-through, no Write-Allocate
	.set _base,_base+1
	.endr
	.rept 0x80 - 0x40   //RO_2 
	 ARM_SECTION_ENTRY _base,0,3,0,0,0 //strong order
	.set _base,_base+1
	.endr
	.rept 0x100 - 0x80   //IO 
	 ARM_SECTION_ENTRY _base,0,3,0,0,0 //strong order
	.set _base,_base+1
	.endr	
	.rept 0x140 - 0x100   //WR_1 
	 ARM_SECTION_ENTRY _base,1,3,0,1,1 //Outer and Inner Write-back, Write-Allocate
	.set _base,_base+1
	.endr
	.rept 0x180 - 0x140   //WR_2 
	 ARM_SECTION_ENTRY _base,1,3,0,1,1 //Outer and Inner Write-back, Write-Allocate
	.set _base,_base+1
	.endr
	.rept 0x1c0 - 0x180   //WR_3 
	 ARM_SECTION_ENTRY _base,1,3,0,1,1 //Outer and Inner Write-back, Write-Allocate
	.set _base,_base+1
	.endr	
	.rept 0x200 - 0x1c0   //WR_4 
	 ARM_SECTION_ENTRY _base,1,3,0,1,1 //Outer and Inner Write-back, Write-Allocate
	.set _base,_base+1
	.endr
	.rept 0x2d0 - 0x200   //SPW 1553B NANDFALSH_REG
	 ARM_SECTION_ENTRY _base,0,3,0,0,0 //strong order
	.set _base,_base+1
	.endr
	.rept 0x300 - 0x2d0   //NANDFALSH_RAM
	 ARM_SECTION_ENTRY _base,0,3,0,0,0 //strong order
	.set _base,_base+1
	.endr
	.rept 0x400 - 0x300   //SPW 1553B NANDFALSH_REG
	 ARM_SECTION_ENTRY _base,0,3,0,0,0 //strong order
	.set _base,_base+1
	.endr
	.rept 0x402 - 0x400   //SRAM
	 ARM_SECTION_ENTRY _base,1,3,0,1,1 //Outer and Inner Write-through, no Write-Allocate
	.set _base,_base+1
	.endr
	.rept 0x404 - 0x402   //SRAM
	 ARM_SECTION_ENTRY _base,0,3,0,0,0 //Outer and Inner Write-through, no Write-Allocate
	.set _base,_base+1
	.endr
	.rept 0xC00 - 0x404   //
	 ARM_SECTION_ENTRY _base,0,3,0,0,0 //strong order
	.set _base,_base+1
	.endr
	.rept 0x1000 - 0xc00   //
	 ARM_SECTION_ENTRY _base,1,3,0,1,1  //strong order
	.set _base,_base+1
	.endr
	
_mmu_table_base:
    .word mmu_table
//finish
#endif

	


ENTRY(lowlevel_init)

	/* branch by CPU ID */
	mrc     p15, 0, r0, c0, c0, 5   @ MPIDR (Multiprocessor Affinity Register)
	and     r0, r0, #0x3
	cmp     r0, #0x0
	beq     primary_cpu
0:
	wfe
	mov     r0, #0
	bx      r0                      @ r0: entry point of U-Boot main for the secondary CPU
primary_cpu:


	//Set bits [11:10] of the NSACR for access to CP10 and CP11 from both Secure and
	//Non-secure states:
	MRC p15, 0, r0, c1, c1, 2
	ORR r0, r0, #0x2<<10 //; enable fpu/neon
	MCR p15, 0, r0, c1, c1, 2
//Set the CPACR for access to CP10 and CP11:
	LDR r0, =(0xF << 20)
	MCR p15, 0, r0, c1, c0, 2
	//Set the FPEXC EN bit to enable the FPU:
	MOV r3, #0x40000000
	VMSR FPEXC, r3


	// init sp
	ldr      sp, =STACK_TOP

	// init localbus cs1 and cs2
	ldr      r1, =LOCALBUS_REG
	ldr      r0, =(ORMEM_WS  | ORMEM_BS | OR_PG_EN | OR_RMW_EN | OR_BITSIZE)  // Modified by LSL, 20190702
	str      r0, [r1, #4]
	ldr      r0, =(IO_WS | IO_WT | IO_RDY | IO_DIVD | IO_FILTER)  // Modified by LSL, 20190702
	str      r0, [r1, #8]
	ldr      r0, =(WRMEM_WS  | WRMEM_BS | WRMEM_RMW | WR_BITSIZE)  // Modified by LSL, 20190702
	str      r0, [r1, #0]
#ifdef DCache_on
	ldr	r3, _mmu_table_base
	bl	enable_mmu              //add by LXG
#endif
	bl       c_main

	mov      r8, #4
	bl       uart_out

	b        .
ENDPROC(lowlevel_init)

//add by LXG
#ifdef DCache_on
ENTRY(enable_mmu)
	mrc	p15, 0, r0, c2, c0, 2	@ TTBCR (Translation Table Base Control Register)
	bic	r0, r0, #0x37
	orr	r0, r0, #0x20		@ disable TTBR1
	mcr	p15, 0, r0, c2, c0, 2

	orr	r0, r3, #0x8		@ Outer Cacheability for table walks: WBWA
	mcr	p15, 0, r0, c2, c0, 0   @ TTBR0

	mov	r0, #0
	mcr	p15, 0, r0, c8, c7, 0	@ invalidate TLBs

	mov	r0, #-1			@ manager for all domains (No permission check)
	mcr	p15, 0, r0, c3, c0, 0   @ DACR (Domain Access Control Register)

	dsb
	isb
	/*
	 * MMU on:
	 * TLBs was already invalidated in "../start.S"
	 * So, we don't need to invalidate it here.
	 */
	mrc	p15, 0, r0, c1, c0, 0	@ SCTLR (System Contrl Register)
	orr	r0, r0, #(CR_C | CR_M)	@ MMU and Dcache enable
	mcr	p15, 0, r0, c1, c0, 0

	mov	pc, lr
ENDPROC(enable_mmu)
#endif

//finish

ENTRY(stack_test)
	//push {r0}
	push {r1, r2, lr}
	
	ldr r8, =echo_hello
	bl       puts

	pop {r1, r2, lr}
	//pop {r0}
	bx       lr
ENDPROC(stack_test)

// if output to tube, this byte will stop the simulation
ENTRY(stop_sim)
	ldr      r8, =0x4
	bl       uart_out
	bx       lr
ENDPROC(stop_sim)

/* void memcpy(void *dst, void *src, int size) */
ENTRY(memcpy)
	add      r2, r2, #0x1f
	lsr      r2, r2, #5
1:
//	ldr      r3, [r1], #4
//	str      r3, [r0], #4
	ldmia    r1!, {r3-r9}
	stmia    r0!, {r3-r9}

	subs     r2, r2, #1
	bne      1b
	bx       lr
ENDPROC(memcpy)

ENTRY(uart_init)
#ifndef OUT_TUBE
	//push     {r0, r1}
	ldr      r9, =UART_BASE
	mov      r8, #0x83
	str      r8, [r9, #0x0c]
	mov      r8, #0xb
	str      r8, [r9, #0x00]
	mov      r8, #0x00
	str      r8, [r9, #0x04]
	mov      r8, #0x03
	str      r8, [r9, #0x0c]
	//pop      {r0, r1}
#endif
	bx       lr
ENDPROC(uart_init)

ENTRY(uart_out)
#ifndef OUT_TUBE
	//push     {r0, r1, r2}
	ldr      r9, =UART_BASE
1:
	ldr      r10, [r9, #0x14]
	tst      r10, #0x20
	beq      1b
	str      r8, [r9]
	//pop      {r0, r1, r2}
#else
	ldr      r9, =TUBE_BASE
	str      r8, [r9]
#endif
	bx       lr
ENDPROC(uart_out)

ENTRY(puts)
	//push     {r0 - r5}
	mov      r12, r8
	mov      r11, lr
1:
	ldrb     r8, [r12], #1
	cmp      r8, #0
	beq      2f
	bl       uart_out
	b        1b
2:
	mov      lr, r11
	//pop      {r0 - r5}
	bx       lr
ENDPROC(puts)

ENTRY(putnum)
	//push     {r0 - r6, lr}
	mov      r12, r8
	mov      r11, lr
	
	lsr      r8, r12, #28
	and      r8, r8, #0xf
	cmp      r8, #0xa
	addge    r8, r8, #55
	addlt    r8, r8, #48
	bl       uart_out
	
	lsr      r8, r12, #24
	and      r8, r8, #0xf
	cmp      r8, #0xa
	addge    r8, r8, #55
	addlt    r8, r8, #48
	bl       uart_out
	
	lsr      r8, r12, #20
	and      r8, r8, #0xf
	cmp      r8, #0xa
	addge    r8, r8, #55
	addlt    r8, r8, #48
	bl       uart_out
	
	lsr      r8, r12, #16
	and      r8, r8, #0xf
	cmp      r8, #0xa
	addge    r8, r8, #55
	addlt    r8, r8, #48
	bl       uart_out
	
	lsr      r8, r12, #12
	and      r8, r8, #0xf
	cmp      r8, #0xa
	addge    r8, r8, #55
	addlt    r8, r8, #48
	bl       uart_out
	
	lsr      r8, r12, #8
	and      r8, r8, #0xf
	cmp      r8, #0xa
	addge    r8, r8, #55
	addlt    r8, r8, #48
	bl       uart_out
	
	lsr      r8, r12, #4
	and      r8, r8, #0xf
	cmp      r8, #0xa
	addge    r8, r8, #55
	addlt    r8, r8, #48
	bl       uart_out
	
	lsr      r8, r12, #0
	and      r8, r8, #0xf
	cmp      r8, #0xa
	addge    r8, r8, #55
	addlt    r8, r8, #48
	bl       uart_out

#ifdef OUT_TUBE
	mov      r8, #0x0a
	bl       uart_out
#endif
	
	mov      lr, r11
	//pop      {r0 - r6, lr}
	bx       lr
ENDPROC(putnum)

ENTRY(newline)
	//push     {r0, lr}
	mov      r11, lr
	mov      r8, #0x0d
	bl       uart_out
	mov      r8, #0x0a
	bl       uart_out
	mov      lr, r11
	//pop      {r0, lr}
	bx       lr
ENDPROC(newline)

ENTRY(readl)
	mov      r7, r8
	mov      r13, lr
	
	bl       putnum        // print the addrss
	ldr      r8, =echo_equal
	bl       puts          // print " = "
	ldr      r8, [r7]
	bl       putnum        // print the vale
	bl       newline       // print enter

	mov      lr, r13
	bx       lr
ENDPROC(readl)


echo_entry_info:
	.ascii  "SoC502 test begin.\r\n"
	.long 0x00000000
	.align 4

echo_equal:
	.ascii  " = "
	.long 0x00000000
	.align 4

echo_ddr_init_begin:
	.ascii  "DDR init begin...\r\n"
	.long 0x00000000
	.align 4

echo_ddr_init_end:
	.ascii  "DDR init end.\r\n"
	.long 0x00000000
	.align 4

echo_hello:
	.ascii  "Hello world!\n"
	.long 0x00000000
	.align 4

