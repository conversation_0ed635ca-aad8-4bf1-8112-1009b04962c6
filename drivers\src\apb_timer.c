
#include "soc502_parameter.h"
#include "types.h"
#include "apb_timer.h"
#include "irq_gic.h"

/*
private timer and watchdog register:
00 load
04 counter
08 control
0C interrupt status

30 wathcdog reset status
34 wathcdog disable 
*/
/*
global timer :
00 lower load
04 upper load
08 control
0C interrupt status

10/14 compator
18 increment
*/


u8 rlt_tmp;

//timer中断处理函数
#if 1
void timer_handle_irq(void)
{
/*	
	unsigned int counter = 0;

	counter = r32(TIMER1_BASE_ADDR+testcnt*0x14 +0x04);

	print2("Timer%d Current Value = 0x%x\r\n",testcnt,counter);
	print2("Timer%d IRQ Status = 0x%x\r\n",testcnt,r32(TIMER1_BASE_ADDR+testcnt*0x14 +0x10));
	print2("Timers IRQ Status = 0x%x\r\n",r32(TIMER1_BASE_ADDR+testcnt*0x14 +0xa0));
	print2("TimersRawIntStatus = 0x%x\r\n",r32(TIMER1_BASE_ADDR+testcnt*0x14 +0xa8));
*/
//	print2("gic: timer0 handle irq \r\n");
//	print2("Timer0 IRQ Status = 0x%x\r\n",r32(TIMER1_BASE_ADDR +0x10));
	rlt_tmp = 0x0;
	r32(TIMER1_BASE_ADDR+ 0x0C);
}
#endif
//////////////////////////////////////////////
// basic functions
/////////////////////////////////////////////
