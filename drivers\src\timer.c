
#include "soc502_parameter.h"
#include "types.h"
#include "irq_gic.h"
#include "FreeRTOS.h"
#include "task.h"


/*
private timer and watchdog register:
00 load
04 counter
08 control
0C interrupt status

30 wathcdog reset status
34 wathcdog disable 
*/
/*
global timer :
00 lower load
04 upper load
08 control
0C interrupt status

10/14 compator
18 increment
*/

u8 rlt;
//private timer中断处理函数
void private_timer_handle_irq(void)
{
	unsigned int irq,intStatus = 0;

//	print2("\r\ngic: private timer handle irq \r\n");
	//intStatus = r32(A9_PRIVATE_BASE+0x04);
//	print2("\r\nprivate timer irq Status = 0x%x\r\n",r32(A9_PRIVATE_BASE+0x0C));
//	print2("\r\nprivate timer counter read data is %x -\r\n",intStatus);

	rlt &= 0x1;

	w32(A9_PRIVATE_BASE + 0x0C,1);
}
//global timer中断处理函数
void global_timer_handle_irq(void)
{
	unsigned int irq,intStatus = 0;

//	intStatus = r32(A9_GLOBAL_TIMER_BASE+0x00);	
//	print2("\r\ngic: global timer handle irq \r\n");
//
//	print2("\r\nglobal timer lower counter read data is %x -\r\n",intStatus);
//	intStatus = r32(A9_GLOBAL_TIMER_BASE+0x04);	
//	print2("\r\nglobal timer upper counter read data is %x -\r\n",intStatus);
//	print2("\r\nglobal timer irq Status = 0x%x\r\n",r32(A9_GLOBAL_TIMER_BASE+0x0C));

	w32(A9_GLOBAL_TIMER_BASE + 0x00,0x00000000);
	w32(A9_GLOBAL_TIMER_BASE + 0x04,0x00000000);
//	w32(A9_GLOBAL_TIMER_BASE + 0x10,0x3B9ACA00);
//	w32(A9_GLOBAL_TIMER_BASE + 0x14,0x00000000);
//	w32(A9_GLOBAL_TIMER_BASE + 0x0C,0x00000001);

	rlt &= 0x2;

	while(r32(A9_GLOBAL_TIMER_BASE + 0x0c))
	{
		w32(A9_GLOBAL_TIMER_BASE + 0x0C,0x00000001);
		w32(A9_GLOBAL_TIMER_BASE + 0x0C,0x00000001);
	}
	
}

//////////////////////////////////////////////
// basic functions
/////////////////////////////////////////////
u32 timer_A9_test()
{
	u32 counter;
	u32 reset;
	u32 lower,upper;	


 	//int cmd;
	gic_set_type(29, IRQ_TYPE_EDGE_RISING);

	gic_register_irq_entry(29, private_timer_handle_irq);
	gic_enable_irq(29);
	
	//private timer test--reload
//	print2("\r\n\r\n------private timer test--auto load------\r\n");

	rlt = 0x3;//bit1:privatetimer err flag  bit0:globaltimer err flag
	
	w32(A9_PRIVATE_BASE + 0x08, 0x00);
//	w32(A9_PRIVATE_BASE + 0x00, 0x1DCD6500);		//write to load register  500MHz,1s
//	w32(A9_PRIVATE_BASE + 0x04, 0x1DCD6500);		//write to load register  500MHz,1s
	w32(A9_PRIVATE_BASE + 0x00, 0x2FAF080);		//write to load register  500MHz,100ms
	w32(A9_PRIVATE_BASE + 0x04, 0x2FAF080);		//write to load register  
	w32(A9_PRIVATE_BASE + 0x08, 0x07);		//control register:enbale interrput/auto load/enbale timer 
	delay_ms(100);
//	counter = r32(A9_PRIVATE_BASE + 0x04);		//read counter register
//	print2("\r\ntimer counter read data is %x -\r\n",counter);


	w32(A9_PRIVATE_BASE + 0x08, 0x00);



	//global timer:auto increment, step=0
//	print2("\r\n\r\n------global timer test--auto increment, step=0------\r\n");

	gic_set_type(27, IRQ_TYPE_EDGE_RISING);
//	gic_set_type(27, IRQ_TYPE_LEVEL_HIGH);
	gic_register_irq_entry(27, global_timer_handle_irq);
	gic_enable_irq(27);

	w32(A9_GLOBAL_TIMER_BASE + 0x08,0x00);				//write control reg:00
	w32(A9_GLOBAL_TIMER_BASE + 0x00,0x0);		//write load register
	w32(A9_GLOBAL_TIMER_BASE + 0x04,0x00000000);
//	w32(A9_GLOBAL_TIMER_BASE + 0x10,0x1DCD6500);		//write compare register:500MHz,1s
	w32(A9_GLOBAL_TIMER_BASE + 0x10,0x2FAF080);		//write compare register:500MHz,100ms
	w32(A9_GLOBAL_TIMER_BASE + 0x14,0x000000000);
	w32(A9_GLOBAL_TIMER_BASE + 0x18,0x000000000);
	w32(A9_GLOBAL_TIMER_BASE + 0x0c,0x00000001);
	w32(A9_GLOBAL_TIMER_BASE + 0x08,0x0F);		//auto increment/enable interrupt/enbale compare/enbale global timer

	SYS_Delay(150000);
	w32(A9_GLOBAL_TIMER_BASE + 0x08,0x00);	

	return rlt;
}
//////////////////////////////////////////////
// FreeRTOS Tick Interrupt Functions
/////////////////////////////////////////////

/*
 * FreeRTOS Tick中断清除函数
 * 清除Cortex-A9 Private Timer的中断标志
 */
void vClearTickInterrupt(void)
{
    /* 清除Private Timer中断状态寄存器 */
    w32(A9_PRIVATE_BASE + 0x0C, 1);
}

/*
 * FreeRTOS Tick中断配置函数
 * 配置Cortex-A9 Private Timer作为FreeRTOS系统tick源
 */
void vConfigureTickInterrupt(void)
{
    uint32_t ulCompareMatch;

    /* 计算Private Timer的重载值
     * Private Timer时钟 = CPU时钟 / (Prescaler + 1)
     * CPU时钟 = 500MHz, Prescaler = 249
     * Private Timer时钟 = 500MHz / 250 = 2MHz
     * 对于configTICK_RATE_HZ (1000Hz): 2MHz / 1000Hz = 2000
     */
    ulCompareMatch = (500000000UL / 250UL) / configTICK_RATE_HZ;

    /* 配置GIC中断控制器 */
    gic_set_type(IRQ_PRIVATE_TIMER, IRQ_TYPE_EDGE_RISING);
    gic_register_irq_entry(IRQ_PRIVATE_TIMER, FreeRTOS_Tick_Handler);
    gic_enable_irq(IRQ_PRIVATE_TIMER);

    /* 配置Private Timer */
    w32(A9_PRIVATE_BASE + 0x08, 0x00);              /* 停止定时器 */
    w32(A9_PRIVATE_BASE + 0x00, ulCompareMatch);    /* 设置重载值 */
    w32(A9_PRIVATE_BASE + 0x04, ulCompareMatch);    /* 设置计数器初值 */

    /* 启动Private Timer
     * 控制寄存器位定义:
     * [15:8] Prescaler = 249 (500MHz / 250 = 2MHz)
     * [2] IRQ enable = 1
     * [1] Auto-reload = 1
     * [0] Timer enable = 1
     */
    w32(A9_PRIVATE_BASE + 0x08, (249 << 8) | (1 << 2) | (1 << 1) | (1 << 0));
}

void vApplicationIRQHandler( uint32_t ulICCIAR )
{
    freertos_gic_handle_irq(ulICCIAR);
}

