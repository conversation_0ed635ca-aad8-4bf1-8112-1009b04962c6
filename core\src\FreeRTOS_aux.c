#include "FreeRTOS_aux.h"
#include <stdio.h>

/***************************************通用程序开始********************************************************/
/* 可视化当前系统中线程运行状态 */
#if (( configUSE_TRACE_FACILITY == 1 ) && ( configUSE_STATS_FORMATTING_FUNCTIONS == 1 ))
void getSysInfo(char *buffer) {
    vTaskList(buffer);
}

/* 任务函数，每隔一秒打印出系统当前线程使用情况 */
void printTaskInfo() {
    for (;;) {
        char mytaskstatebuffer[500];
        vTaskSuspendAll();
        {
            printf("==========================================\r\n");
            printf("name\t\t\tstatus\tpri\tstack\tid\n");
            getSysInfo((char*)&mytaskstatebuffer);
            printf("%s\n",mytaskstatebuffer);

        }
        xTaskResumeAll();
        vTaskDelay(pdMS_TO_TICKS(1000));
    }
}

#endif


/* 任务堆栈溢出后，会调用此处的钩子函数 */
#if (configCHECK_FOR_STACK_OVERFLOW > 0)
void vApplicationStackOverflowHook(TaskHandle_t xTask, char *pcTaskName)
{
    /* Run time stack overflow checking is performed if
    configCHECK_FOR_STACK_OVERFLOW is defined to 1 or 2. This hook function is
    called if a stack overflow is detected. */
    printf("task: %s stack overflow\n",pcTaskName);
}
#endif

/* 静态分配开启时，任务调度器创建IDLE任务时需要使用此函数分配IDLE任务的TCB空间和运行栈空间 */
#if (configSUPPORT_STATIC_ALLOCATION == 1)
void vApplicationGetIdleTaskMemory(StaticTask_t **ppxIdleTaskTCBBuffer,
                                   StackType_t **ppxIdleTaskStackBuffer,
                                   uint32_t *pulIdleTaskStackSize)
{
    /* If the buffers to be provided to the Idle task are declared inside this
    function then they must be declared static - otherwise they will be allocated on
    the stack and so not exists after this function exits. */
    static StaticTask_t xIdleTaskTCB;
    static StackType_t uxIdleTaskStack[configMINIMAL_STACK_SIZE];

    /* Pass out a pointer to the StaticTask_t structure in which the Idle task's
    state will be stored. */
    *ppxIdleTaskTCBBuffer = &xIdleTaskTCB;

    /* Pass out the array that will be used as the Idle task's stack. */
    *ppxIdleTaskStackBuffer = uxIdleTaskStack;

    /* Pass out the size of the array pointed to by *ppxIdleTaskStackBuffer.
    Note that, as the array is necessarily of type StackType_t,
    configMINIMAL_STACK_SIZE is specified in words, not bytes. */
    *pulIdleTaskStackSize = configMINIMAL_STACK_SIZE;
}
#endif

/* configSUPPORT_STATIC_ALLOCATION和configUSE_TIMERS开启时，需要此函数为定时器任务分配TCB空间和运行栈空间 */
#if (configSUPPORT_STATIC_ALLOCATION == 1 && configUSE_TIMERS == 1)
/* configSUPPORT_STATIC_ALLOCATION and configUSE_TIMERS are both set to 1, so the
application must provide an implementation of vApplicationGetTimerTaskMemory()
to provide the memory that is used by the Timer service task. */
void vApplicationGetTimerTaskMemory(StaticTask_t **ppxTimerTaskTCBBuffer,
                                    StackType_t **ppxTimerTaskStackBuffer,
                                    uint32_t *puxTimerTaskStackSize)
{
    /* If the buffers to be provided to the Timer task are declared inside this
    function then they must be declared static - otherwise they will be allocated on
    the stack and so not exists after this function exits. */
    static StaticTask_t xTimerTaskTCB;
    static StackType_t uxTimerTaskStack[configTIMER_TASK_STACK_DEPTH];

    /* Pass out a pointer to the StaticTask_t structure in which the Timer
    task's state will be stored. */
    *ppxTimerTaskTCBBuffer = &xTimerTaskTCB;

    /* Pass out the array that will be used as the Timer task's stack. */
    * ppxTimerTaskStackBuffer = uxTimerTaskStack;

    /* Pass out the size of the array pointed to by *ppxTimerTaskStackBuffer.
    Note that, as the array is necessarily of type StackType_t,
    configTIMER_TASK_STACK_DEPTH is specified in words, not bytes. */
    *puxTimerTaskStackSize = configTIMER_TASK_STACK_DEPTH;
}

#endif
/***************************************通用程序结束********************************************************/