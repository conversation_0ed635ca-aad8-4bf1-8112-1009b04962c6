# FreeRTOS for Cortex-A9 - Windows Build Environment
# arm-none-eabi-gcc-9.2.1
GCCPATH         ?= F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32

CROSS_COMPILE   ?= $(GCCPATH)/bin/arm-none-eabi-

TARGET          ?= soc2018_freertos

CC              := $(CROSS_COMPILE)gcc
CXX             := $(CROSS_COMPILE)g++
LD              := $(CROSS_COMPILE)gcc
OBJCOPY         := $(CROSS_COMPILE)objcopy
OBJDUMP         := $(CROSS_COMPILE)objdump
SIZE            := $(CROSS_COMPILE)size

# Windows环境下的库路径设置
LIBPATH         := -lgcc -L "$(GCCPATH)/lib/gcc/arm-none-eabi/9.2.1" \
                   -L "$(GCCPATH)/arm-none-eabi/lib"

INCDIRS         :=
INCDIRS         += core/inc
INCDIRS         += drivers/inc
INCDIRS         += freertos \
                   freertos/include \
                   freertos/portable/GCC/ARM_CA9 \
                   freertos/portable/MemMang

SRCDIRS         :=
SRCDIRS         += core/src
SRCDIRS         += drivers/src
SRCDIRS         += freertos \
                   freertos/portable/GCC/ARM_CA9 \
                   freertos/portable/MemMang

INCLUDE         := $(patsubst %, -I %, $(INCDIRS))

SFILES          := $(foreach dir, $(SRCDIRS), $(wildcard $(dir)/*.S))
CFILES          := $(foreach dir, $(SRCDIRS), $(wildcard $(dir)/*.c))
CPPFILES        := $(foreach dir, $(SRCDIRS), $(wildcard $(dir)/*.cpp))

SFILENDIR       := $(notdir  $(SFILES))
CFILENDIR       := $(notdir  $(CFILES))
CPPFILENDIR     := $(notdir  $(CPPFILES))

SOBJS           := $(patsubst %, obj/%, $(SFILENDIR:.S=.o))
COBJS           := $(patsubst %, obj/%, $(CFILENDIR:.c=.o))
CPPOBJS         := $(patsubst %, obj/%, $(CPPFILENDIR:.cpp=.o))
OBJS            := $(SOBJS) $(COBJS) $(CPPOBJS)

VPATH           := $(SRCDIRS)

# --- 平台兼容处理 ---
ifeq ($(OS),Windows_NT)
    MKDIR   = if not exist obj mkdir obj
    RM      = del /Q
    RMDIR   = rmdir /S /Q
    NULLDEV = nul
else
    MKDIR   = mkdir -p obj
    RM      = rm -f
    RMDIR   = rm -rf
    NULLDEV = /dev/null
endif

# 创建 obj 目录
$(shell $(MKDIR))

.PHONY: clean all

# Cortex-A9 编译选项
CPU_FLAGS       := -mcpu=cortex-a9 -mtune=cortex-a9 -marm
FPU_FLAGS       := -mfpu=neon -mfloat-abi=softfp
COMMON_FLAGS    := $(CPU_FLAGS) $(FPU_FLAGS) -Wall -O2 -g
ASM_FLAGS       := $(COMMON_FLAGS) -D__ASSEMBLY__
C_FLAGS         := $(COMMON_FLAGS) -std=gnu99
CXX_FLAGS       := $(COMMON_FLAGS) -std=gnu++11 -fno-exceptions -fno-rtti

# 链接选项
LDFLAGS         := -g -nostartfiles -T link_script.ld $(CPU_FLAGS) $(FPU_FLAGS) \
                   --specs=nosys.specs -Wl,--gc-sections -Wl,-Map,$(TARGET).map

# link
$(TARGET).bin : $(OBJS)
	@echo "Linking $(TARGET).elf..."
	$(LD) $(LDFLAGS) -o $(TARGET).elf $^ $(LIBPATH) -lnosys -lstdc++ -lc -lm
	$(OBJCOPY) -O binary -S $(TARGET).elf $@
	$(OBJDUMP) -D -m arm $(TARGET).elf > $(TARGET).dis
	$(SIZE) $(TARGET).elf
	@echo "Build completed: $(TARGET).bin"

# assembly
$(SOBJS) : obj/%.o : %.S
	@echo "Assembling $<..."
	$(CC) $(ASM_FLAGS) -c $(INCLUDE) -o $@ $<

# c compile
$(COBJS) : obj/%.o : %.c
	@echo "Compiling $<..."
	$(CC) $(C_FLAGS) -c $(INCLUDE) -o $@ $<

# cpp compile
$(CPPOBJS) : obj/%.o : %.cpp
	@echo "Compiling $<..."
	$(CXX) $(CXX_FLAGS) -c $(INCLUDE) -o $@ $<

# 添加调试和分析目标
debug: $(TARGET).bin
	@echo "Debug information:"
	@echo "ELF file: $(TARGET).elf"
	@echo "Binary file: $(TARGET).bin"
	@echo "Disassembly: $(TARGET).dis"
	@echo "Memory map: $(TARGET).map"

# 显示内存使用情况
meminfo: $(TARGET).elf
	$(SIZE) -A -t $(TARGET).elf

# 清理目标
clean:
	@echo "Cleaning build files..."
	-$(RM) $(TARGET).elf 2>$(NULLDEV)
	-$(RM) $(TARGET).dis 2>$(NULLDEV)
	-$(RM) $(TARGET).bin 2>$(NULLDEV)
	-$(RM) $(TARGET).map 2>$(NULLDEV)
	-$(RM) load.imx 2>$(NULLDEV)
	-$(RMDIR) obj 2>$(NULLDEV)
	@echo "Clean completed."

# 帮助信息
help:
	@echo "Available targets:"
	@echo "  all      - Build the project (default)"
	@echo "  clean    - Remove all build files"
	@echo "  debug    - Show debug information"
	@echo "  meminfo  - Show memory usage information"
	@echo "  help     - Show this help message"

.PHONY: clean debug meminfo help all
all: $(TARGET).bin
	