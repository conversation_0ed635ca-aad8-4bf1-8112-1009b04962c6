Archive member included to satisfy reference by file (symbol)

F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_udivsi3.o)
                              obj/main.o (__aeabi_uidiv)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_divsi3.o)
                              obj/main.o (__aeabi_idiv)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_dvmd_tls.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_udivsi3.o) (__aeabi_idiv0)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_fixunsdfdi.o)
                              obj/BSPPrint.o (__aeabi_d2ulz)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_muldf3.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_fixunsdfdi.o) (__aeabi_dmul)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_addsubdf3.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_fixunsdfdi.o) (__aeabi_dsub)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_fixunsdfsi.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_fixunsdfdi.o) (__aeabi_d2uiz)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memset.o)
                              obj/tasks.o (memset)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sprintf.o)
                              obj/tasks.o (sprintf)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-stpcpy.o)
                              obj/tasks.o (stpcpy)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strlen-stub.o)
                              obj/tasks.o (strlen)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sprintf.o) (_svfprintf_r)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-dtoa.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o) (_dtoa_r)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-freer.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o) (_free_r)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-impure.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sprintf.o) (_impure_ptr)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-localeconv.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o) (_localeconv_r)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mallocr.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o) (_malloc_r)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memchr-stub.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o) (memchr)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mlock.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-freer.o) (__malloc_lock)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-dtoa.o) (_Balloc)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-s_frexp.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o) (frexp)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sbrkr.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-freer.o) (_sbrk_r)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strncpy.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o) (strncpy)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfiprintf.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o) (__ssprint_r)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-callocr.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o) (_calloc_r)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-locale.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-localeconv.o) (__global_locale)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mlock.o) (__retarget_lock_acquire_recursive)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mbtowc_r.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-locale.o) (__ascii_mbtowc)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memmove.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfiprintf.o) (memmove)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-reallocr.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfiprintf.o) (_realloc_r)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-reent.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sbrkr.o) (errno)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strcmp.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-locale.o) (strcmp)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-wctomb_r.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-locale.o) (__ascii_wctomb)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-ctype_.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-locale.o) (_ctype_)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_muldivdf3.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-dtoa.o) (__aeabi_ddiv)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_cmpdf2.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o) (__aeabi_dcmpeq)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_unorddf2.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o) (__aeabi_dcmpun)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_fixdfsi.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o) (__aeabi_d2iz)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_aeabi_uldivmod.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o) (__aeabi_uldivmod)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_udivmoddi4.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_aeabi_uldivmod.o) (__udivmoddi4)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_clzdi2.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_udivmoddi4.o) (__clzdi2)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_clzsi2.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_clzdi2.o) (__clzsi2)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libnosys.a(sbrk.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sbrkr.o) (_sbrk)

Allocating common symbols
Common symbol       size              file

rdata               0x4               obj/gic_handle_irq.o
__lock___atexit_recursive_mutex
                    0x1               F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
__lock___arc4random_mutex
                    0x1               F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
StartTask_Handler   0x4               obj/main.o
errno               0x4               F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-reent.o)
rlt                 0x1               obj/timer.o
fiq_num             0x4               obj/gic_handle_irq.o
xQueueRegistry      0x40              obj/queue.o
error_temp          0x4               obj/FreeRTOS_aux.o
__lock___env_recursive_mutex
                    0x1               F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
__lock___sinit_recursive_mutex
                    0x1               F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
irq_num             0x4               obj/gic_handle_irq.o
__lock___malloc_recursive_mutex
                    0x1               F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
StartTask_Handler2  0x4               obj/main.o
__lock___at_quick_exit_mutex
                    0x1               F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
__lock___dd_hash_mutex
                    0x1               F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
__lock___tz_mutex   0x1               F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
rdata2              0x4               obj/gic_handle_irq.o
__lock___sfp_recursive_mutex
                    0x1               F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
rlt_tmp             0x1               obj/apb_timer.o

Discarded input sections

 .data          0x00000000        0x0 obj/lowlevel.o
 .bss           0x00000000        0x0 obj/lowlevel.o
 .text          0x00000000       0x38 obj/mem_clear.o
 .data          0x00000000        0x0 obj/mem_clear.o
 .bss           0x00000000        0x0 obj/mem_clear.o
 .debug_line    0x00000000       0x54 obj/mem_clear.o
 .debug_info    0x00000000       0x26 obj/mem_clear.o
 .debug_abbrev  0x00000000       0x14 obj/mem_clear.o
 .debug_aranges
                0x00000000       0x20 obj/mem_clear.o
 .debug_str     0x00000000       0x4b obj/mem_clear.o
 .ARM.attributes
                0x00000000       0x25 obj/mem_clear.o
 .data          0x00000000        0x0 obj/start.o
 .bss           0x00000000        0x0 obj/start.o
 .text          0x00000000        0x0 obj/vectors.o
 .data          0x00000000        0x0 obj/vectors.o
 .bss           0x00000000        0x0 obj/vectors.o
 .debug_line    0x00000000       0x80 obj/vectors.o
 .debug_info    0x00000000       0x26 obj/vectors.o
 .debug_abbrev  0x00000000       0x14 obj/vectors.o
 .debug_aranges
                0x00000000       0x20 obj/vectors.o
 .debug_str     0x00000000       0x49 obj/vectors.o
 .ARM.attributes
                0x00000000       0x25 obj/vectors.o
 .data          0x00000000        0x0 obj/portASM.o
 .bss           0x00000000        0x0 obj/portASM.o
 .text          0x00000000       0x50 obj/FreeRTOS_aux.o
 .data          0x00000000        0x0 obj/FreeRTOS_aux.o
 .bss           0x00000000        0x0 obj/FreeRTOS_aux.o
 .rodata.str1.4
                0x00000000       0x47 obj/FreeRTOS_aux.o
 .debug_info    0x00000000      0xaf9 obj/FreeRTOS_aux.o
 .debug_abbrev  0x00000000      0x2cd obj/FreeRTOS_aux.o
 .debug_loc     0x00000000       0x5a obj/FreeRTOS_aux.o
 .debug_aranges
                0x00000000       0x20 obj/FreeRTOS_aux.o
 .debug_ranges  0x00000000       0x28 obj/FreeRTOS_aux.o
 .debug_line    0x00000000      0x2a7 obj/FreeRTOS_aux.o
 .debug_str     0x00000000      0x641 obj/FreeRTOS_aux.o
 .comment       0x00000000       0x7a obj/FreeRTOS_aux.o
 .debug_frame   0x00000000       0x3c obj/FreeRTOS_aux.o
 .ARM.attributes
                0x00000000       0x37 obj/FreeRTOS_aux.o
 COMMON         0x00000000        0x4 obj/FreeRTOS_aux.o
 .data          0x00000000        0x0 obj/main.o
 .bss           0x00000000        0x8 obj/main.o
 .rodata        0x00000000    0x1c028 obj/main.o
 .text          0x00000000       0x20 obj/apb_timer.o
 .data          0x00000000        0x0 obj/apb_timer.o
 .bss           0x00000000        0x0 obj/apb_timer.o
 .debug_info    0x00000000       0xf3 obj/apb_timer.o
 .debug_abbrev  0x00000000       0xb8 obj/apb_timer.o
 .debug_aranges
                0x00000000       0x20 obj/apb_timer.o
 .debug_line    0x00000000       0x8e obj/apb_timer.o
 .debug_str     0x00000000      0x1b0 obj/apb_timer.o
 .comment       0x00000000       0x7a obj/apb_timer.o
 .debug_frame   0x00000000       0x20 obj/apb_timer.o
 .ARM.attributes
                0x00000000       0x37 obj/apb_timer.o
 COMMON         0x00000000        0x1 obj/apb_timer.o
 .data          0x00000000        0x0 obj/BSPPrint.o
 .bss           0x00000000        0x0 obj/BSPPrint.o
 .data          0x00000000        0x0 obj/gic_handle_irq.o
 .bss           0x00000000        0x4 obj/gic_handle_irq.o
 .text          0x00000000      0x2e8 obj/gic_irq.o
 .data          0x00000000        0x0 obj/gic_irq.o
 .bss           0x00000000        0x0 obj/gic_irq.o
 .rodata.str1.4
                0x00000000       0x30 obj/gic_irq.o
 .debug_info    0x00000000      0x933 obj/gic_irq.o
 .debug_abbrev  0x00000000      0x274 obj/gic_irq.o
 .debug_loc     0x00000000      0x7de obj/gic_irq.o
 .debug_aranges
                0x00000000       0x20 obj/gic_irq.o
 .debug_ranges  0x00000000      0x2b8 obj/gic_irq.o
 .debug_line    0x00000000      0x421 obj/gic_irq.o
 .debug_str     0x00000000      0x34e obj/gic_irq.o
 .comment       0x00000000       0x7a obj/gic_irq.o
 .debug_frame   0x00000000      0x140 obj/gic_irq.o
 .ARM.attributes
                0x00000000       0x37 obj/gic_irq.o
 .data          0x00000000        0x0 obj/irq_gic.o
 .data          0x00000000        0x0 obj/mprintf.o
 .bss           0x00000000        0x0 obj/mprintf.o
 .data          0x00000000        0x0 obj/sram_on_chip_test.o
 .bss           0x00000000        0x0 obj/sram_on_chip_test.o
 .data          0x00000000        0x0 obj/timer.o
 .bss           0x00000000        0x0 obj/timer.o
 .text          0x00000000       0xec obj/uart.o
 .data          0x00000000        0x0 obj/uart.o
 .bss           0x00000000        0x0 obj/uart.o
 .debug_info    0x00000000      0x29f obj/uart.o
 .debug_abbrev  0x00000000      0x1ad obj/uart.o
 .debug_loc     0x00000000      0x11d obj/uart.o
 .debug_aranges
                0x00000000       0x20 obj/uart.o
 .debug_ranges  0x00000000       0x18 obj/uart.o
 .debug_line    0x00000000      0x15a obj/uart.o
 .debug_str     0x00000000      0x1ff obj/uart.o
 .comment       0x00000000       0x7a obj/uart.o
 .debug_frame   0x00000000       0x70 obj/uart.o
 .ARM.attributes
                0x00000000       0x37 obj/uart.o
 .data          0x00000000        0x0 obj/list.o
 .bss           0x00000000        0x0 obj/list.o
 .text          0x00000000      0xce8 obj/queue.o
 .data          0x00000000        0x0 obj/queue.o
 .bss           0x00000000        0x0 obj/queue.o
 .debug_info    0x00000000     0x2457 obj/queue.o
 .debug_abbrev  0x00000000      0x45d obj/queue.o
 .debug_loc     0x00000000     0x13ba obj/queue.o
 .debug_aranges
                0x00000000       0x20 obj/queue.o
 .debug_ranges  0x00000000      0x240 obj/queue.o
 .debug_line    0x00000000     0x12c4 obj/queue.o
 .debug_str     0x00000000      0xd49 obj/queue.o
 .comment       0x00000000       0x7a obj/queue.o
 .debug_frame   0x00000000      0x2bc obj/queue.o
 .ARM.attributes
                0x00000000       0x37 obj/queue.o
 COMMON         0x00000000       0x40 obj/queue.o
 .data          0x00000000        0x0 obj/heap_4.o
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_udivsi3.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_udivsi3.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_divsi3.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_divsi3.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_dvmd_tls.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_dvmd_tls.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_fixunsdfdi.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_fixunsdfdi.o)
 .text          0x00000000      0x290 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_muldf3.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_muldf3.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_muldf3.o)
 .debug_frame   0x00000000       0x30 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_muldf3.o)
 .ARM.attributes
                0x00000000       0x1c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_muldf3.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_addsubdf3.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_addsubdf3.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_fixunsdfsi.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_fixunsdfsi.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memset.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memset.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memset.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sprintf.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sprintf.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sprintf.o)
 .text._sprintf_r
                0x00000000       0x5c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sprintf.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-stpcpy.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-stpcpy.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-stpcpy.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strlen-stub.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strlen-stub.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strlen-stub.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-dtoa.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-dtoa.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-dtoa.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-freer.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-freer.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-freer.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-impure.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-impure.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-impure.o)
 .rodata._global_impure_ptr
                0x00000000        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-impure.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-localeconv.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-localeconv.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-localeconv.o)
 .text.__localeconv_l
                0x00000000        0x8 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-localeconv.o)
 .text.localeconv
                0x00000000        0xc F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-localeconv.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mallocr.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mallocr.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mallocr.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memchr-stub.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memchr-stub.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memchr-stub.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mlock.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mlock.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mlock.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
 .text.__s2b    0x00000000       0xf0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
 .text.__ulp    0x00000000       0x64 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
 .text.__b2d    0x00000000       0xe0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
 .text.__ratio  0x00000000       0x84 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
 .text._mprec_log10
                0x00000000       0x5c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
 .text.__copybits
                0x00000000       0x70 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
 .text.__any_on
                0x00000000       0x64 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
 .rodata.__mprec_tinytens
                0x00000000       0x28 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-s_frexp.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-s_frexp.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-s_frexp.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sbrkr.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sbrkr.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sbrkr.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strncpy.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strncpy.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strncpy.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfiprintf.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfiprintf.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfiprintf.o)
 .rodata._svfiprintf_r.str1.4
                0x00000000       0x2f F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfiprintf.o)
 .text._svfiprintf_r
                0x00000000     0x1114 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfiprintf.o)
 .rodata.blanks.7324
                0x00000000       0x10 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfiprintf.o)
 .rodata.zeroes.7325
                0x00000000       0x10 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfiprintf.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-callocr.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-callocr.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-callocr.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-locale.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-locale.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-locale.o)
 .text._setlocale_r
                0x00000000       0x68 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-locale.o)
 .text.__locale_mb_cur_max
                0x00000000       0x10 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-locale.o)
 .text.setlocale
                0x00000000       0x18 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-locale.o)
 .bss._PathLocale
                0x00000000        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-locale.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
 .text.__retarget_lock_init
                0x00000000        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
 .text.__retarget_lock_init_recursive
                0x00000000        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
 .text.__retarget_lock_close
                0x00000000        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
 .text.__retarget_lock_close_recursive
                0x00000000        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
 .text.__retarget_lock_acquire
                0x00000000        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
 .text.__retarget_lock_try_acquire
                0x00000000        0x8 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
 .text.__retarget_lock_try_acquire_recursive
                0x00000000        0x8 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
 .text.__retarget_lock_release
                0x00000000        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mbtowc_r.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mbtowc_r.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mbtowc_r.o)
 .text._mbtowc_r
                0x00000000       0x1c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mbtowc_r.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memmove.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memmove.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memmove.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-reallocr.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-reallocr.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-reallocr.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-reent.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-reent.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-reent.o)
 .text.cleanup_glue
                0x00000000       0x2c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-reent.o)
 .text._reclaim_reent
                0x00000000       0xf0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-reent.o)
 .text          0x00000000      0x224 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strcmp.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strcmp.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strcmp.o)
 .debug_frame   0x00000000       0x40 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strcmp.o)
 .ARM.attributes
                0x00000000       0x1a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strcmp.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-wctomb_r.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-wctomb_r.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-wctomb_r.o)
 .text._wctomb_r
                0x00000000       0x1c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-wctomb_r.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-ctype_.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-ctype_.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-ctype_.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_muldivdf3.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_muldivdf3.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_cmpdf2.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_cmpdf2.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_unorddf2.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_unorddf2.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_fixdfsi.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_fixdfsi.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_aeabi_uldivmod.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_aeabi_uldivmod.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_udivmoddi4.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_udivmoddi4.o)
 .ARM.extab     0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_udivmoddi4.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_clzdi2.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_clzdi2.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_clzsi2.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_clzsi2.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libnosys.a(sbrk.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libnosys.a(sbrk.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libnosys.a(sbrk.o)

Memory Configuration

Name             Origin             Length             Attributes
RAM              0x40018000         0x08000000         xrw
*default*        0x00000000         0xffffffff

Linker script and memory map

LOAD obj/lowlevel.o
LOAD obj/mem_clear.o
LOAD obj/start.o
LOAD obj/vectors.o
LOAD obj/portASM.o
LOAD obj/FreeRTOS_aux.o
LOAD obj/main.o
LOAD obj/apb_timer.o
LOAD obj/BSPPrint.o
LOAD obj/gic_handle_irq.o
LOAD obj/gic_irq.o
LOAD obj/irq_gic.o
LOAD obj/mprintf.o
LOAD obj/sram_on_chip_test.o
LOAD obj/timer.o
LOAD obj/uart.o
LOAD obj/list.o
LOAD obj/queue.o
LOAD obj/tasks.o
LOAD obj/port.o
LOAD obj/heap_4.o
LOAD F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a
LOAD F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libnosys.a
LOAD F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libstdc++.a
LOAD F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a
LOAD F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libm.a
START GROUP
LOAD F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a
LOAD F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libg.a
LOAD F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a
END GROUP
START GROUP
LOAD F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a
LOAD F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a
LOAD F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libnosys.a
END GROUP

.text           0x40018000     0xbe34
                0x40018000                __text_start__ = .
 obj/vectors.o(.vectors)
 .vectors       0x40018000      0x24c obj/vectors.o
                0x40018000                _start
                0x40018020                _undefined_instruction
                0x40018024                _software_interrupt
                0x40018028                _prefetch_abort
                0x4001802c                _data_abort
                0x40018030                _not_used
                0x40018034                _irq
                0x40018038                _fiq
                0x40018040                IRQ_STACK_START_IN
                0x40018044                FIQ_STACK_START_IN
 *(.text*)
 *fill*         0x4001824c        0x4 
 .text          0x40018250      0x2a0 obj/lowlevel.o
                0x40018250                lowlevel_init
                0x400182b8                stack_test
                0x400182cc                stop_sim
                0x400182d8                memcpy
                0x400182f4                uart_init
                0x400182f8                uart_out
                0x40018304                puts
                0x40018328                putnum
                0x40018400                newline
                0x4001841c                readl
 .text          0x400184f0       0x9c obj/start.o
                0x400184f0                reset
                0x40018530                c_runtime_cpu_setup
                0x40018548                save_boot_params
                0x4001854c                cpu_init_cp15
                0x40018584                cpu_init_crit
 *fill*         0x4001858c        0x4 
 .text          0x40018590      0x2e0 obj/portASM.o
                0x40018590                FreeRTOS_SWI_Handler
                0x40018638                vPortRestoreTaskContext
                0x400186a0                FreeRTOS_IRQ_Handler
 .text          0x40018870      0x4a8 obj/main.o
                0x40018870                start_task
                0x40018890                start_task2
                0x400188b0                printDouble
                0x40018a38                _write
                0x40018a70                MoniterRead
                0x40018a88                freertos_start
                0x40018af4                c_main
                0x40018b60                apb_uart_init
                0x40018bb8                outbyte
                0x40018be4                SYS_Delay
                0x40018c28                get_tb_start
                0x40018c54                get_tb_end
                0x40018c64                get_tb_diff
                0x40018c6c                delayus
                0x40018cc0                delay_ms
 .text          0x40018d18      0x2d0 obj/BSPPrint.o
                0x40018dc4                print2
                0x40018fd0                pchar
 .text          0x40018fe8      0x308 obj/gic_handle_irq.o
                0x40018fe8                gic_handle_fiq
                0x400191bc                apb_timer1_int_service
                0x40019228                do_undefined_instruction
                0x40019250                do_software_interrupt
                0x4001928c                do_prefetch_abort
                0x400192cc                do_data_abort
                0x400192e4                do_not_used
 .text          0x400192f0      0x388 obj/irq_gic.o
                0x400192f0                gic_get_irq_number
                0x40019304                gic_enable_irq
                0x40019328                gic_disable_irq
                0x4001934c                gic_eoi_irq
                0x4001935c                gic_send_sgi
                0x40019374                gic_global_enable
                0x40019388                gic_global_disable
                0x400193bc                gic_dist_config
                0x400194a8                gic_configure_irq
                0x4001951c                gic_set_type
                0x40019534                gic_handle_irq_init
                0x40019540                gic_register_irq_entry
                0x40019554                gic_remove_irq_entry
                0x4001956c                gic_handle_irq
                0x400195ac                gic_dist_init
                0x400195f4                gic_cpu_init
                0x40019610                gic_cpu_config
                0x40019614                disable_irq
                0x40019624                enable_irq
                0x40019634                gic_init
                0x40019670                SWI_Enable
 .text          0x40019678      0x67c obj/mprintf.o
                0x400197ec                printnum
                0x40019820                print
                0x40019848                isdigit_m
                0x4001985c                tolower_m
                0x4001986c                strlen_m
                0x40019894                mprintf
                0x40019c70                printreg
                0x40019cbc                printval
 .text          0x40019cf4      0xac8 obj/sram_on_chip_test.o
                0x40019cf4                sram_dancuo_irq_handler
                0x40019d48                sram_duocuo_irq_handler
                0x40019dd0                sram_on_chip_regtest
                0x40019e60                sram_on_chip_rw_test
                0x40019f38                sram_on_chip_bypass_test
                0x4001a03c                sram_on_chip_dancuo_test
                0x4001a4f0                sram_on_chip_duocuo_test
 .text          0x4001a7bc      0x1b0 obj/timer.o
                0x4001a7bc                private_timer_handle_irq
                0x4001a7e4                global_timer_handle_irq
                0x4001a834                timer_A9_test
                0x4001a904                vClearTickInterrupt
                0x4001a918                vConfigureTickInterrupt
                0x4001a968                vApplicationIRQHandler
 .text          0x4001a96c       0xec obj/list.o
                0x4001a96c                vListInitialise
                0x4001a990                vListInitialiseItem
                0x4001a99c                vListInsertEnd
                0x4001a9c8                vListInsert
                0x4001aa20                uxListRemove
 .text          0x4001aa58     0x18b8 obj/tasks.o
                0x4001ae38                xTaskCreate
                0x4001b0b0                vTaskDelete
                0x4001b1bc                eTaskGetState
                0x4001b1dc                uxTaskPriorityGet
                0x4001b208                uxTaskPriorityGetFromISR
                0x4001b234                vTaskPrioritySet
                0x4001b364                vTaskSuspend
                0x4001b46c                vTaskResume
                0x4001b53c                xTaskResumeFromISR
                0x4001b634                vTaskStartScheduler
                0x4001b6ac                vTaskEndScheduler
                0x4001b6cc                vTaskSuspendAll
                0x4001b6e4                xTaskResumeAll
                0x4001b894                xTaskDelayUntil
                0x4001b934                vTaskDelay
                0x4001b978                xTaskGetTickCount
                0x4001b988                xTaskGetTickCountFromISR
                0x4001b998                uxTaskGetNumberOfTasks
                0x4001b9a8                pcTaskGetName
                0x4001b9c0                xTaskCatchUpTicks
                0x4001b9f8                xTaskIncrementTick
                0x4001ba24                vTaskSetApplicationTaskTag
                0x4001ba4c                xTaskGetApplicationTaskTag
                0x4001ba74                xTaskGetApplicationTaskTagFromISR
                0x4001ba9c                xTaskCallApplicationTaskHook
                0x4001bac8                vTaskSwitchContext
                0x4001baec                vTaskPlaceOnEventList
                0x4001bb18                vTaskPlaceOnUnorderedEventList
                0x4001bb8c                xTaskRemoveFromEventList
                0x4001bcc4                vTaskRemoveFromUnorderedEventList
                0x4001bdb0                vTaskSetTimeOutState
                0x4001bdd8                vTaskInternalSetTimeOutState
                0x4001bdf0                xTaskCheckForTimeOut
                0x4001be80                vTaskMissedYield
                0x4001be94                uxTaskGetTaskNumber
                0x4001bea0                vTaskSetTaskNumber
                0x4001beac                vTaskSetThreadLocalStoragePointer
                0x4001bed0                pvTaskGetThreadLocalStoragePointer
                0x4001befc                vTaskGetInfo
                0x4001c068                uxTaskGetSystemState
                0x4001c19c                xTaskGetCurrentTaskHandle
                0x4001c1ac                xTaskGetSchedulerState
                0x4001c1dc                vTaskList
                0x4001c2e8                uxTaskResetEventItemValue
 .text          0x4001c310      0x350 obj/port.o
                0x4001c350                pxPortInitialiseStack
                0x4001c42c                xPortStartScheduler
                0x4001c47c                vPortEndScheduler
                0x4001c480                vPortEnterCritical
                0x4001c4d8                vPortExitCritical
                0x4001c538                FreeRTOS_Tick_Handler
                0x4001c5bc                vPortTaskUsesFPU
                0x4001c5d8                vPortClearInterruptMask
                0x4001c614                ulPortSetInterruptMask
                0x4001c65c                vApplicationFPUSafeIRQHandler
 .text          0x4001c660      0x348 obj/heap_4.o
                0x4001c6dc                pvPortMalloc
                0x4001c83c                vPortFree
                0x4001c8a0                xPortGetFreeHeapSize
                0x4001c8b0                xPortGetMinimumEverFreeHeapSize
                0x4001c8c0                vPortInitialiseBlocks
                0x4001c8c4                pvPortCalloc
                0x4001c908                vPortGetHeapStats
 .text          0x4001c9a8      0x114 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_udivsi3.o)
                0x4001c9a8                __aeabi_uidiv
                0x4001c9a8                __udivsi3
                0x4001ca9c                __aeabi_uidivmod
 .text          0x4001cabc      0x148 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_divsi3.o)
                0x4001cabc                __divsi3
                0x4001cabc                __aeabi_idiv
                0x4001cbe4                __aeabi_idivmod
 .text          0x4001cc04        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_dvmd_tls.o)
                0x4001cc04                __aeabi_idiv0
                0x4001cc04                __aeabi_ldiv0
 .text          0x4001cc08       0x5c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_fixunsdfdi.o)
                0x4001cc08                __fixunsdfdi
                0x4001cc08                __aeabi_d2ulz
 .text          0x4001cc64      0x424 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_addsubdf3.o)
                0x4001cc64                __aeabi_drsub
                0x4001cc6c                __aeabi_dsub
                0x4001cc6c                __subdf3
                0x4001cc70                __aeabi_dadd
                0x4001cc70                __adddf3
                0x4001cf80                __floatunsidf
                0x4001cf80                __aeabi_ui2d
                0x4001cfa4                __floatsidf
                0x4001cfa4                __aeabi_i2d
                0x4001cfcc                __aeabi_f2d
                0x4001cfcc                __extendsfdf2
                0x4001d014                __floatundidf
                0x4001d014                __aeabi_ul2d
                0x4001d028                __floatdidf
                0x4001d028                __aeabi_l2d
 .text          0x4001d088       0x54 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_fixunsdfsi.o)
                0x4001d088                __aeabi_d2uiz
                0x4001d088                __fixunsdfsi
 .text.memset   0x4001d0dc      0x11c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memset.o)
                0x4001d0dc                memset
 .text.sprintf  0x4001d1f8       0x6c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sprintf.o)
                0x4001d1f8                sprintf
 .text.stpcpy   0x4001d264       0x74 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-stpcpy.o)
                0x4001d264                stpcpy
 .text.strlen   0x4001d2d8       0x60 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strlen-stub.o)
                0x4001d2d8                strlen
 .text._svfprintf_r
                0x4001d338     0x27a0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o)
                0x4001d338                _svfprintf_r
 .text.quorem   0x4001fad8      0x1c0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-dtoa.o)
 .text._dtoa_r  0x4001fc98     0x16d4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-dtoa.o)
                0x4001fc98                _dtoa_r
 .text._malloc_trim_r
                0x4002136c      0x100 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-freer.o)
                0x4002136c                _malloc_trim_r
 .text._free_r  0x4002146c      0x2ec F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-freer.o)
                0x4002146c                _free_r
 .text._localeconv_r
                0x40021758        0xc F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-localeconv.o)
                0x40021758                _localeconv_r
 .text._malloc_r
                0x40021764      0x7d4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mallocr.o)
                0x40021764                _malloc_r
 .text.memchr   0x40021f38       0xf8 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memchr-stub.o)
                0x40021f38                memchr
 .text.__malloc_lock
                0x40022030       0x18 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mlock.o)
                0x40022030                __malloc_lock
 .text.__malloc_unlock
                0x40022048       0x18 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mlock.o)
                0x40022048                __malloc_unlock
 .text._Balloc  0x40022060       0x8c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
                0x40022060                _Balloc
 .text._Bfree   0x400220ec       0x1c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
                0x400220ec                _Bfree
 .text.__multadd
                0x40022108       0xd0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
                0x40022108                __multadd
 .text.__hi0bits
                0x400221d8       0x58 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
                0x400221d8                __hi0bits
 .text.__lo0bits
                0x40022230       0xa0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
                0x40022230                __lo0bits
 .text.__i2b    0x400222d0       0x24 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
                0x400222d0                __i2b
 .text.__multiply
                0x400222f4      0x1f0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
                0x400222f4                __multiply
 .text.__pow5mult
                0x400224e4      0x104 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
                0x400224e4                __pow5mult
 .text.__lshift
                0x400225e8      0x118 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
                0x400225e8                __lshift
 .text.__mcmp   0x40022700       0x60 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
                0x40022700                __mcmp
 .text.__mdiff  0x40022760      0x1e4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
                0x40022760                __mdiff
 .text.__d2b    0x40022944      0x118 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
                0x40022944                __d2b
 .text.frexp    0x40022a5c       0xa4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-s_frexp.o)
                0x40022a5c                frexp
 .text._sbrk_r  0x40022b00       0x44 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sbrkr.o)
                0x40022b00                _sbrk_r
 .text.strncpy  0x40022b44       0xcc F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strncpy.o)
                0x40022b44                strncpy
 .text.__ssprint_r
                0x40022c10      0x1a4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfiprintf.o)
                0x40022c10                __ssprint_r
 .text._calloc_r
                0x40022db4       0x9c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-callocr.o)
                0x40022db4                _calloc_r
 .text.__retarget_lock_acquire_recursive
                0x40022e50        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
                0x40022e50                __retarget_lock_acquire_recursive
 .text.__retarget_lock_release_recursive
                0x40022e54        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
                0x40022e54                __retarget_lock_release_recursive
 .text.__ascii_mbtowc
                0x40022e58       0x44 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mbtowc_r.o)
                0x40022e58                __ascii_mbtowc
 .text.memmove  0x40022e9c      0x158 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memmove.o)
                0x40022e9c                memmove
 .text._realloc_r
                0x40022ff4      0x594 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-reallocr.o)
                0x40022ff4                _realloc_r
 .text.__ascii_wctomb
                0x40023588       0x30 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-wctomb_r.o)
                0x40023588                __ascii_wctomb
 .text          0x400235b8      0x49c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_muldivdf3.o)
                0x400235b8                __aeabi_dmul
                0x400235b8                __muldf3
                0x40023848                __divdf3
                0x40023848                __aeabi_ddiv
 .text          0x40023a54      0x144 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_cmpdf2.o)
                0x40023a54                __gtdf2
                0x40023a54                __gedf2
                0x40023a5c                __ltdf2
                0x40023a5c                __ledf2
                0x40023a64                __nedf2
                0x40023a64                __eqdf2
                0x40023a64                __cmpdf2
                0x40023aec                __aeabi_cdrcmple
                0x40023b08                __aeabi_cdcmpeq
                0x40023b08                __aeabi_cdcmple
                0x40023b20                __aeabi_dcmpeq
                0x40023b38                __aeabi_dcmplt
                0x40023b50                __aeabi_dcmple
                0x40023b68                __aeabi_dcmpge
                0x40023b80                __aeabi_dcmpgt
 .text          0x40023b98       0x38 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_unorddf2.o)
                0x40023b98                __unorddf2
                0x40023b98                __aeabi_dcmpun
 .text          0x40023bd0       0x5c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_fixdfsi.o)
                0x40023bd0                __aeabi_d2iz
                0x40023bd0                __fixdfsi
 .text          0x40023c2c       0x3c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_aeabi_uldivmod.o)
                0x40023c2c                __aeabi_uldivmod
 .text          0x40023c68      0x130 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_udivmoddi4.o)
                0x40023c68                __udivmoddi4
 .text          0x40023d98       0x28 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_clzdi2.o)
                0x40023d98                __clzdi2
 .text          0x40023dc0       0x48 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_clzsi2.o)
                0x40023dc0                __clzsi2
 .text._sbrk    0x40023e08       0x2c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libnosys.a(sbrk.o)
                0x40023e08                _sbrk
 *(.glue_7)
 .glue_7        0x40023e34        0x0 linker stubs
 *(.glue_7t)
 .glue_7t       0x40023e34        0x0 linker stubs
 *(.vfp11_veneer)
 .vfp11_veneer  0x40023e34        0x0 linker stubs
 *(.v4_bx)
 .v4_bx         0x40023e34        0x0 linker stubs
                0x40023e34                __text_end__ = .

.iplt           0x40023e34        0x0
 .iplt          0x40023e34        0x0 obj/lowlevel.o

.rodata         0x40023e38      0xcad
                0x40023e38                __rodata_start__ = .
 *(.rodata*)
 .rodata.str1.4
                0x40023e38       0x80 obj/main.o
 .rodata        0x40023eb8       0x10 obj/BSPPrint.o
 .rodata.str1.4
                0x40023ec8      0x32a obj/gic_handle_irq.o
 *fill*         0x400241f2        0x2 
 .rodata.str1.4
                0x400241f4       0x27 obj/irq_gic.o
 *fill*         0x4002421b        0x1 
 .rodata.str1.4
                0x4002421c       0x26 obj/mprintf.o
                                 0x2a (size before relaxing)
 *fill*         0x40024242        0x2 
 .rodata.str1.4
                0x40024244      0x5ff obj/sram_on_chip_test.o
 *fill*         0x40024843        0x1 
 .rodata        0x40024844       0x10 obj/sram_on_chip_test.o
 .rodata.str1.4
                0x40024854       0x17 obj/tasks.o
 *fill*         0x4002486b        0x1 
 .rodata        0x4002486c        0x5 obj/tasks.o
 *fill*         0x40024871        0x3 
 .rodata        0x40024874       0x10 obj/port.o
                0x40024874                ulMaxAPIPriorityMask
                0x40024878                ulICCPMR
                0x4002487c                ulICCEOIR
                0x40024880                ulICCIAR
 .rodata._svfprintf_r.str1.4
                0x40024884       0x2b F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o)
                                 0x42 (size before relaxing)
 *fill*         0x400248af        0x1 
 .rodata.blanks.7345
                0x400248b0       0x10 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o)
 .rodata.zeroes.7346
                0x400248c0       0x10 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o)
 .rodata._dtoa_r.str1.4
                0x400248d0        0xd F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-dtoa.o)
                                 0x12 (size before relaxing)
 *fill*         0x400248dd        0x3 
 .rodata.__mprec_bigtens
                0x400248e0       0x28 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
                0x400248e0                __mprec_bigtens
 .rodata.__mprec_tens
                0x40024908       0xc8 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
                0x40024908                __mprec_tens
 .rodata.p05.6115
                0x400249d0        0xc F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
 .rodata._setlocale_r.str1.4
                0x400249dc        0x6 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-locale.o)
                                  0xd (size before relaxing)
 .rodata.str1.4
                0x400249e2        0x2 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-locale.o)
 *fill*         0x400249e2        0x2 
 .rodata._ctype_
                0x400249e4      0x101 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-ctype_.o)
                0x400249e4                _ctype_
                0x40024ae5                __rodata_end__ = .

.ARM.exidx      0x40024ae8        0x8
 .ARM.exidx     0x40024ae8        0x8 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_udivmoddi4.o)

.rel.dyn        0x40024af0        0x0
 .rel.iplt      0x40024af0        0x0 obj/lowlevel.o

.data           0x40024af0      0x9b4
                0x40024af0                __data_start__ = .
 *(.data*)
 .data          0x40024af0        0x4 obj/tasks.o
                0x40024af0                uxTopUsedPriority
 .data          0x40024af4        0x4 obj/port.o
                0x40024af4                ulCriticalNesting
 .data._impure_ptr
                0x40024af8        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-impure.o)
                0x40024af8                _impure_ptr
 *fill*         0x40024afc        0x4 
 .data.impure_data
                0x40024b00      0x428 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-impure.o)
 .data.__malloc_av_
                0x40024f28      0x408 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mallocr.o)
                0x40024f28                __malloc_av_
 .data.__malloc_sbrk_base
                0x40025330        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mallocr.o)
                0x40025330                __malloc_sbrk_base
 .data.__malloc_trim_threshold
                0x40025334        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mallocr.o)
                0x40025334                __malloc_trim_threshold
 .data.__global_locale
                0x40025338      0x16c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-locale.o)
                0x40025338                __global_locale
                0x400254a4                __data_end__ = .
                0x400254a4                _case_list_start = .

.igot.plt       0x400254a4        0x0
 .igot.plt      0x400254a4        0x0 obj/lowlevel.o

.test_case
 *(.test_case)
                0x400254a4                _case_list_end = .

.bss            0x400254a4     0xcc28
                0x400254a4                __bss_start__ = .
 *(.bss*)
 .bss           0x400254a4      0x280 obj/irq_gic.o
 .bss           0x40025724      0x104 obj/tasks.o
                0x40025744                pxCurrentTCB
 .bss           0x40025828        0xc obj/port.o
                0x40025828                ulPortYieldRequired
                0x4002582c                ulPortTaskHasFPUContext
                0x40025830                ulPortInterruptNesting
 .bss           0x40025834     0xc81c obj/heap_4.o
 .bss.__malloc_current_mallinfo
                0x40032050       0x28 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mallocr.o)
                0x40032050                __malloc_current_mallinfo
 .bss.__malloc_max_sbrked_mem
                0x40032078        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mallocr.o)
                0x40032078                __malloc_max_sbrked_mem
 .bss.__malloc_max_total_mem
                0x4003207c        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mallocr.o)
                0x4003207c                __malloc_max_total_mem
 .bss.__malloc_top_pad
                0x40032080        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mallocr.o)
                0x40032080                __malloc_top_pad
 .bss.heap_end.4144
                0x40032084        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libnosys.a(sbrk.o)
 *(COMMON)
 COMMON         0x40032088        0x8 obj/main.o
                0x40032088                StartTask_Handler
                0x4003208c                StartTask_Handler2
 COMMON         0x40032090       0x10 obj/gic_handle_irq.o
                0x40032090                rdata
                0x40032094                fiq_num
                0x40032098                irq_num
                0x4003209c                rdata2
 COMMON         0x400320a0        0x1 obj/timer.o
                0x400320a0                rlt
 *fill*         0x400320a1        0x3 
 COMMON         0x400320a4       0x21 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
                0x400320a4                __lock___atexit_recursive_mutex
                0x400320a8                __lock___arc4random_mutex
                0x400320ac                __lock___env_recursive_mutex
                0x400320b0                __lock___sinit_recursive_mutex
                0x400320b4                __lock___malloc_recursive_mutex
                0x400320b8                __lock___at_quick_exit_mutex
                0x400320bc                __lock___dd_hash_mutex
                0x400320c0                __lock___tz_mutex
                0x400320c4                __lock___sfp_recursive_mutex
 *fill*         0x400320c5        0x3 
 COMMON         0x400320c8        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-reent.o)
                0x400320c8                errno
                0x400320cc                __bss_end__ = .

.heap           0x400320cc   0x100000
                0x400320cc                __heap_start__ = .
                0x400320cc                __end__ = .
                0x400320cc                PROVIDE (end = .)
 *(.heap*)
                0x401320cc                . = (. + 0x100000)
 *fill*         0x400320cc   0x100000 
                0x401320cc                __heap_end__ = .
                0x401320cc                __HeapLimit = __heap_end__

.stack          0x401320d0    0x10000
                0x401320d0                __stack_start__ = .
                0x401420d0                . = (. + 0x10000)
 *fill*         0x401320d0    0x10000 
                0x401420d0                __stack_end__ = .
                0x401420d0                __StackTop = __stack_end__

.debug_info     0x00000000     0xa04e
 *(.debug_info)
 .debug_info    0x00000000       0x26 obj/lowlevel.o
 .debug_info    0x00000026       0x26 obj/start.o
 .debug_info    0x0000004c       0x26 obj/portASM.o
 .debug_info    0x00000072     0x1163 obj/main.o
 .debug_info    0x000011d5      0x4f8 obj/BSPPrint.o
 .debug_info    0x000016cd      0x506 obj/gic_handle_irq.o
 .debug_info    0x00001bd3      0x683 obj/irq_gic.o
 .debug_info    0x00002256     0x12b3 obj/mprintf.o
 .debug_info    0x00003509      0xd01 obj/sram_on_chip_test.o
 .debug_info    0x0000420a      0x376 obj/timer.o
 .debug_info    0x00004580      0xbe4 obj/list.o
 .debug_info    0x00005164     0x3325 obj/tasks.o
 .debug_info    0x00008489      0xccd obj/port.o
 .debug_info    0x00009156      0xef8 obj/heap_4.o

.debug_abbrev   0x00000000     0x237b
 *(.debug_abbrev)
 .debug_abbrev  0x00000000       0x14 obj/lowlevel.o
 .debug_abbrev  0x00000014       0x14 obj/start.o
 .debug_abbrev  0x00000028       0x14 obj/portASM.o
 .debug_abbrev  0x0000003c      0x4a5 obj/main.o
 .debug_abbrev  0x000004e1      0x278 obj/BSPPrint.o
 .debug_abbrev  0x00000759      0x18b obj/gic_handle_irq.o
 .debug_abbrev  0x000008e4      0x368 obj/irq_gic.o
 .debug_abbrev  0x00000c4c      0x43f obj/mprintf.o
 .debug_abbrev  0x0000108b      0x20f obj/sram_on_chip_test.o
 .debug_abbrev  0x0000129a      0x1c1 obj/timer.o
 .debug_abbrev  0x0000145b      0x26b obj/list.o
 .debug_abbrev  0x000016c6      0x58c obj/tasks.o
 .debug_abbrev  0x00001c52      0x34d obj/port.o
 .debug_abbrev  0x00001f9f      0x3dc obj/heap_4.o

.debug_line     0x00000000     0x54b7
 *(.debug_line)
 .debug_line    0x00000000       0xdd obj/lowlevel.o
 .debug_line    0x000000dd       0x76 obj/start.o
 .debug_line    0x00000153       0xaf obj/portASM.o
 .debug_line    0x00000202      0x703 obj/main.o
 .debug_line    0x00000905      0x416 obj/BSPPrint.o
 .debug_line    0x00000d1b      0x2f1 obj/gic_handle_irq.o
 .debug_line    0x0000100c      0x4e6 obj/irq_gic.o
 .debug_line    0x000014f2      0x6c1 obj/mprintf.o
 .debug_line    0x00001bb3      0xb6e obj/sram_on_chip_test.o
 .debug_line    0x00002721      0x287 obj/timer.o
 .debug_line    0x000029a8      0x3f6 obj/list.o
 .debug_line    0x00002d9e     0x1aec obj/tasks.o
 .debug_line    0x0000488a      0x57a obj/port.o
 .debug_line    0x00004e04      0x6b3 obj/heap_4.o

.debug_frame    0x00000000     0x1cec
 *(.debug_frame)
 .debug_frame   0x00000000      0x17c obj/main.o
 .debug_frame   0x0000017c       0x94 obj/BSPPrint.o
 .debug_frame   0x00000210       0xd4 obj/gic_handle_irq.o
 .debug_frame   0x000002e4      0x188 obj/irq_gic.o
 .debug_frame   0x0000046c      0x158 obj/mprintf.o
 .debug_frame   0x000005c4      0x118 obj/sram_on_chip_test.o
 .debug_frame   0x000006dc       0x88 obj/timer.o
 .debug_frame   0x00000764       0x68 obj/list.o
 .debug_frame   0x000007cc      0x554 obj/tasks.o
 .debug_frame   0x00000d20       0xe0 obj/port.o
 .debug_frame   0x00000e00       0xe4 obj/heap_4.o
 .debug_frame   0x00000ee4       0x20 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_udivsi3.o)
 .debug_frame   0x00000f04       0x20 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_divsi3.o)
 .debug_frame   0x00000f24       0x34 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_fixunsdfdi.o)
 .debug_frame   0x00000f58       0xac F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_addsubdf3.o)
 .debug_frame   0x00001004       0x24 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_fixunsdfsi.o)
 .debug_frame   0x00001028       0x50 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memset.o)
 .debug_frame   0x00001078       0x6c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sprintf.o)
 .debug_frame   0x000010e4       0x2c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-stpcpy.o)
 .debug_frame   0x00001110       0x20 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strlen-stub.o)
 .debug_frame   0x00001130       0x50 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o)
 .debug_frame   0x00001180       0xc0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-dtoa.o)
 .debug_frame   0x00001240       0x74 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-freer.o)
 .debug_frame   0x000012b4       0x40 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-localeconv.o)
 .debug_frame   0x000012f4       0x74 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mallocr.o)
 .debug_frame   0x00001368       0x4c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memchr-stub.o)
 .debug_frame   0x000013b4       0x48 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mlock.o)
 .debug_frame   0x000013fc      0x2fc F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
 .debug_frame   0x000016f8       0x34 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-s_frexp.o)
 .debug_frame   0x0000172c       0x3c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sbrkr.o)
 .debug_frame   0x00001768       0x40 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strncpy.o)
 .debug_frame   0x000017a8       0xa0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfiprintf.o)
 .debug_frame   0x00001848       0x34 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-callocr.o)
 .debug_frame   0x0000187c       0x50 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-locale.o)
 .debug_frame   0x000018cc       0xb0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
 .debug_frame   0x0000197c       0x48 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mbtowc_r.o)
 .debug_frame   0x000019c4       0x34 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memmove.o)
 .debug_frame   0x000019f8       0x70 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-reallocr.o)
 .debug_frame   0x00001a68       0x64 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-reent.o)
 .debug_frame   0x00001acc       0x3c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-wctomb_r.o)
 .debug_frame   0x00001b08       0x50 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_muldivdf3.o)
 .debug_frame   0x00001b58       0xc4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_cmpdf2.o)
 .debug_frame   0x00001c1c       0x20 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_unorddf2.o)
 .debug_frame   0x00001c3c       0x24 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_fixdfsi.o)
 .debug_frame   0x00001c60       0x2c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_aeabi_uldivmod.o)
 .debug_frame   0x00001c8c       0x40 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_udivmoddi4.o)
 .debug_frame   0x00001ccc       0x20 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libnosys.a(sbrk.o)

.debug_str      0x00000000     0x2140
 *(.debug_str)
 .debug_str     0x00000000       0x4a obj/lowlevel.o
 .debug_str     0x0000004a       0x14 obj/start.o
                                 0x47 (size before relaxing)
 .debug_str     0x0000005e       0x28 obj/portASM.o
                                 0x5b (size before relaxing)
 .debug_str     0x00000086      0x778 obj/main.o
                                0x817 (size before relaxing)
 .debug_str     0x000007fe      0x209 obj/BSPPrint.o
                                0x360 (size before relaxing)
 .debug_str     0x00000a07      0x164 obj/gic_handle_irq.o
                                0x2e3 (size before relaxing)
 .debug_str     0x00000b6b      0x18c obj/irq_gic.o
                                0x373 (size before relaxing)
 .debug_str     0x00000cf7      0x111 obj/mprintf.o
                                0x6f7 (size before relaxing)
 .debug_str     0x00000e08       0xda obj/sram_on_chip_test.o
                                0x2cc (size before relaxing)
 .debug_str     0x00000ee2       0xd2 obj/timer.o
                                0x2fa (size before relaxing)
 .debug_str     0x00000fb4      0x137 obj/list.o
                                0x6fd (size before relaxing)
 .debug_str     0x000010eb      0xc59 obj/tasks.o
                               0x13ec (size before relaxing)
 .debug_str     0x00001d44      0x145 obj/port.o
                                0x81d (size before relaxing)
 .debug_str     0x00001e89      0x2b7 obj/heap_4.o
                                0x921 (size before relaxing)

.debug_loc      0x00000000     0x4404
 *(.debug_loc)
 .debug_loc     0x00000000      0x5f5 obj/main.o
 .debug_loc     0x000005f5      0x3d9 obj/BSPPrint.o
 .debug_loc     0x000009ce       0xba obj/gic_handle_irq.o
 .debug_loc     0x00000a88      0x472 obj/irq_gic.o
 .debug_loc     0x00000efa      0xa8d obj/mprintf.o
 .debug_loc     0x00001987      0x587 obj/sram_on_chip_test.o
 .debug_loc     0x00001f0e       0x25 obj/timer.o
 .debug_loc     0x00001f33       0x93 obj/list.o
 .debug_loc     0x00001fc6     0x1d13 obj/tasks.o
 .debug_loc     0x00003cd9      0x21b obj/port.o
 .debug_loc     0x00003ef4      0x510 obj/heap_4.o

.debug_aranges  0x00000000      0x1c0
 *(.debug_aranges)
 .debug_aranges
                0x00000000       0x20 obj/lowlevel.o
 .debug_aranges
                0x00000020       0x20 obj/start.o
 .debug_aranges
                0x00000040       0x20 obj/portASM.o
 .debug_aranges
                0x00000060       0x20 obj/main.o
 .debug_aranges
                0x00000080       0x20 obj/BSPPrint.o
 .debug_aranges
                0x000000a0       0x20 obj/gic_handle_irq.o
 .debug_aranges
                0x000000c0       0x20 obj/irq_gic.o
 .debug_aranges
                0x000000e0       0x20 obj/mprintf.o
 .debug_aranges
                0x00000100       0x20 obj/sram_on_chip_test.o
 .debug_aranges
                0x00000120       0x20 obj/timer.o
 .debug_aranges
                0x00000140       0x20 obj/list.o
 .debug_aranges
                0x00000160       0x20 obj/tasks.o
 .debug_aranges
                0x00000180       0x20 obj/port.o
 .debug_aranges
                0x000001a0       0x20 obj/heap_4.o

.debug_ranges   0x00000000      0x908
 *(.debug_ranges)
 .debug_ranges  0x00000000       0xd8 obj/main.o
 .debug_ranges  0x000000d8       0x88 obj/BSPPrint.o
 .debug_ranges  0x00000160       0x70 obj/irq_gic.o
 .debug_ranges  0x000001d0      0x120 obj/mprintf.o
 .debug_ranges  0x000002f0       0x48 obj/sram_on_chip_test.o
 .debug_ranges  0x00000338      0x5d0 obj/tasks.o

.debug_macinfo
 *(.debug_macinfo)

.comment        0x00000000       0x79
 *(.comment)
 .comment       0x00000000       0x79 obj/main.o
                                 0x7a (size before relaxing)
 .comment       0x00000079       0x7a obj/BSPPrint.o
 .comment       0x00000079       0x7a obj/gic_handle_irq.o
 .comment       0x00000079       0x7a obj/irq_gic.o
 .comment       0x00000079       0x7a obj/mprintf.o
 .comment       0x00000079       0x7a obj/sram_on_chip_test.o
 .comment       0x00000079       0x7a obj/timer.o
 .comment       0x00000079       0x7a obj/list.o
 .comment       0x00000079       0x7a obj/tasks.o
 .comment       0x00000079       0x7a obj/port.o
 .comment       0x00000079       0x7a obj/heap_4.o

.ARM.attributes
                0x00000000       0x33
 *(.ARM.attributes)
 .ARM.attributes
                0x00000000       0x25 obj/lowlevel.o
 .ARM.attributes
                0x00000025       0x25 obj/start.o
 .ARM.attributes
                0x0000004a       0x27 obj/portASM.o
 .ARM.attributes
                0x00000071       0x37 obj/main.o
 .ARM.attributes
                0x000000a8       0x37 obj/BSPPrint.o
 .ARM.attributes
                0x000000df       0x37 obj/gic_handle_irq.o
 .ARM.attributes
                0x00000116       0x37 obj/irq_gic.o
 .ARM.attributes
                0x0000014d       0x37 obj/mprintf.o
 .ARM.attributes
                0x00000184       0x37 obj/sram_on_chip_test.o
 .ARM.attributes
                0x000001bb       0x37 obj/timer.o
 .ARM.attributes
                0x000001f2       0x37 obj/list.o
 .ARM.attributes
                0x00000229       0x37 obj/tasks.o
 .ARM.attributes
                0x00000260       0x37 obj/port.o
 .ARM.attributes
                0x00000297       0x37 obj/heap_4.o
 .ARM.attributes
                0x000002ce       0x1c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_udivsi3.o)
 .ARM.attributes
                0x000002ea       0x1c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_divsi3.o)
 .ARM.attributes
                0x00000306       0x1c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_dvmd_tls.o)
 .ARM.attributes
                0x00000322       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_fixunsdfdi.o)
 .ARM.attributes
                0x0000034c       0x1c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_addsubdf3.o)
 .ARM.attributes
                0x00000368       0x1c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_fixunsdfsi.o)
 .ARM.attributes
                0x00000384       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memset.o)
 .ARM.attributes
                0x000003ae       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sprintf.o)
 .ARM.attributes
                0x000003d8       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-stpcpy.o)
 .ARM.attributes
                0x00000402       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strlen-stub.o)
 .ARM.attributes
                0x0000042c       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o)
 .ARM.attributes
                0x00000456       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-dtoa.o)
 .ARM.attributes
                0x00000480       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-freer.o)
 .ARM.attributes
                0x000004aa       0x30 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-impure.o)
 .ARM.attributes
                0x000004da       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-localeconv.o)
 .ARM.attributes
                0x00000504       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mallocr.o)
 .ARM.attributes
                0x0000052e       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memchr-stub.o)
 .ARM.attributes
                0x00000558       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mlock.o)
 .ARM.attributes
                0x00000582       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
 .ARM.attributes
                0x000005ac       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-s_frexp.o)
 .ARM.attributes
                0x000005d6       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sbrkr.o)
 .ARM.attributes
                0x00000600       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strncpy.o)
 .ARM.attributes
                0x0000062a       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfiprintf.o)
 .ARM.attributes
                0x00000654       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-callocr.o)
 .ARM.attributes
                0x0000067e       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-locale.o)
 .ARM.attributes
                0x000006a8       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
 .ARM.attributes
                0x000006d2       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mbtowc_r.o)
 .ARM.attributes
                0x000006fc       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memmove.o)
 .ARM.attributes
                0x00000726       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-reallocr.o)
 .ARM.attributes
                0x00000750       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-reent.o)
 .ARM.attributes
                0x0000077a       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-wctomb_r.o)
 .ARM.attributes
                0x000007a4       0x30 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-ctype_.o)
 .ARM.attributes
                0x000007d4       0x1c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_muldivdf3.o)
 .ARM.attributes
                0x000007f0       0x1c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_cmpdf2.o)
 .ARM.attributes
                0x0000080c       0x1c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_unorddf2.o)
 .ARM.attributes
                0x00000828       0x1c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_fixdfsi.o)
 .ARM.attributes
                0x00000844       0x1c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_aeabi_uldivmod.o)
 .ARM.attributes
                0x00000860       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_udivmoddi4.o)
 .ARM.attributes
                0x0000088a       0x1c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_clzdi2.o)
 .ARM.attributes
                0x000008a6       0x1c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_clzsi2.o)
 .ARM.attributes
                0x000008c2       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libnosys.a(sbrk.o)
OUTPUT(soc2018_freertos.elf elf32-littlearm)
