#ifndef _IRQ_GIC_H
#define _IRQ_GIC_H

//
// gic registers' address
//
#define GIC_CPU_CTRL                    0x00
#define GIC_CPU_PRIMASK                 0x04
#define GIC_CPU_BINPOINT                0x08
#define GIC_CPU_INTACK                  0x0c
#define GIC_CPU_EOI                     0x10
#define GIC_CPU_RUNNINGPRI              0x14
#define GIC_CPU_HIGHPRI                 0x18
#define GIC_CPU_ALAIS_BINPOINT          0x1c
#define GIC_CPU_ACTIVEPRIO              0xd0
#define GIC_CPU_IDENT                   0xfc
#define GICC_IAR_INT_ID_MASK            0x3ff

#define GIC_DIST_BASE                   0x3FFF1000
#define GIC_CPU_BASE                    0x3FFF0100

#define GIC_DIST_CTRL                   0x000
#define GIC_DIST_CTL                    0x004
//#define GIC_DIST_IGROUP                 0x080
#define GIC_DIST_Security                 0x080
#define GIC_DIST_ENABLE_SET             0x100
#define GIC_DIST_ENABLE_CLEAR           0x180
#define GIC_DIST_PENDING_SET            0x200
#define GIC_DIST_PENDING_CLEAR          0x280
#define GIC_DIST_ACTIVE_SET             0x300
#define GIC_DIST_ACTIVE_CLEAR           0x380
#define GIC_DIST_PRI                    0x400
#define GIC_DIST_TARGET                 0x800
#define GIC_DIST_CONFIG                 0xc00
#define GIC_DIST_SOFTINT                0xf00
#define GIC_DIST_SGI_PENDING_CLEAR      0xf10
#define GIC_DIST_SGI_PENDING_SET        0xf20

#define IRQ_TYPE_EDGE_RISING            1
#define IRQ_TYPE_LEVEL_HIGH             4

#define MAX_IRQS                        160

//
// Irq number
//
#define IRQ_GLOBAL_TIMER                27
#define IRQ_PRIVATE_TIMER               29
#define IRQ_PRIVATE_WDT                 30
#define IRQ_UART00                      32
#define IRQ_UART01                      33
#define IRQ_UART02                      34
#define IRQ_UART03                      35
#define IRQ_UART04                      36
#define IRQ_UART05                      37
#define IRQ_UART06                      38
#define IRQ_UART07                      39
#define IRQ_UART08                      40
#define IRQ_UART09                      41
#define IRQ_UART10                      42
#define IRQ_UART11                      43
#define IRQ_IIC0                        44
#define IRQ_IIC1                        45
#define IRQ_IIC2                        46
#define IRQ_IIC3                        47
#define IRQ_SPI_M0                      48
#define IRQ_SPI_M1                      49
#define IRQ_SPI_S0                      50
#define IRQ_SPI_S1                      51
#define IRQ_GPIO                        52
#define IRQ_TIMER                       53
#define IRQ_CAN0                        54
#define IRQ_CAN1                        55
#define IRQ_PWM0                        56
#define IRQ_PWM1                        57
#define IRQ_PWM2                        58
#define IRQ_PWM3                        59
#define IRQ_EPWMTZINT0                  60
#define IRQ_EPWMTZINT1                  61
#define IRQ_EPWMTZINT2                  62
#define IRQ_EPWMTZINT3                  63
#define IRQ_ECAP0                       64
#define IRQ_ECAP1                       65
#define IRQ_ECAP2                       66
#define IRQ_ECAP3                       67
#define IRQ_ECAP4                       68
#define IRQ_ECAP5                       69
#define IRQ_WDT0                        70
#define IRQ_WDT1                        71
#define IRQ_H0_DMA0			72
#define IRQ_H0_DMA1			73	
#define IRQ_H0_DMA2			74
#define IRQ_H0_DMA3			75
#define IRQ_GMAC0                       76
#define IRQ_GMAC1                       77
#define IRQ_PCIE0                       78
#define IRQ_PCIE1                       79
#define IRQ_EMMC                        80

//
// gic driver api
//
typedef void (*int_handle_func_ptr)(u32 irq_num);

unsigned int gic_get_irq_number(void);
void gic_enable_irq(unsigned int irq);
void gic_disable_irq(unsigned int irq);
void gic_eoi_irq(unsigned int irq);
void gic_set_type(unsigned int irq, unsigned int type);
void gic_send_sgi(unsigned int cpu_id, unsigned int irq);
void gic_configure_irq(unsigned int type, unsigned int irq);
void gic_dist_init(void);
void gic_cpu_init(void);

void enable_irq(void);
void disable_irq(void);
void gic_init(void);

void gic_register_irq_entry(unsigned int irq, int_handle_func_ptr int_handle);
void gic_remove_irq_entry(unsigned int irq);
void gic_handle_irq(void);
void gic_handle_fiq(void);
#endif

