#ifndef __RESET_H__
#define __RESET_H__

#include "common.h"

typedef struct
{
    __IO uint32_t soft_reset;        //0x000
    __IO uint8_t  RESERVED1[9];
    __IO uint32_t cpu0_rst_flag;     //0x010
    __IO uint32_t cpu1_rst_flag;     //0x014
    __IO uint32_t RESERVED2[2];
    __IO uint32_t cpu0_neon_flag;    //0x020
    __IO uint32_t cpu1_neon_flag;    //0x024
    __IO uint32_t RESERVED3[2];
    __IO uint32_t cpu0_dbg_rst_flag; //0x030
    __IO uint32_t cpu1_dbg_rst_flag; //0x034
    __IO uint32_t RESERVED4[2];
    __IO uint32_t cpu0_wdg_rst_flag; //0x040
    __IO uint32_t cpu1_wdg_rst_flag; //0x044
    __IO uint32_t RESERVED5[2];
    __IO uint32_t cpu_scu_rst_flag;  //0x050
    __IO uint32_t cpu_periph_rst_flag;//0x054
    __IO uint32_t RESERVED6[42];
    __IO uint32_t cpu_dbg_rst_common; //0x100
    __IO uint32_t cpu_dbg_rst_set;    //0x104
    __IO uint32_t cpu_dbg_rst_clear;  //0x108
    __IO uint32_t cpu_dbg_rst_toggle; //0x10c
    __IO uint32_t cpu_wdg_rst_common; //0x110
    __IO uint32_t cpu_wdg_rst_set;    //0x114
    __IO uint32_t cpu_wdg_rst_clear;  //0x118
    __IO uint32_t cpu_wdg_rst_toggle; //0x11c
    __IO uint32_t cpu_aux_rst_common;     //0x120
    __IO uint32_t cpu_aux_rst_set;        //0x124
    __IO uint32_t cpu_aux_rst_clear;      //0x128
    __IO uint32_t cpu_aux_rst_toggle;     //0x12c
    __IO uint32_t RESERVED7[52];
    __IO uint32_t axi_periph_rst_common; //0x200
    __IO uint32_t axi_periph_rst_set;    //0x204
    __IO uint32_t axi_periph_rst_clear;  //0x208
    __IO uint32_t axi_periph_rst_toggle; //0x20c
    __IO uint32_t ips_rst_0_common;      //0x210 
    __IO uint32_t ips_rst_0_set;         //0x214
    __IO uint32_t ips_rst_0_clear;       //0x218
    __IO uint32_t ips_rst_0_toggle;      //0x21c
    __IO uint32_t ips_rst_1_common;      //0x220 
    __IO uint32_t ips_rst_1_set;         //0x224
    __IO uint32_t ips_rst_1_clear;       //0x228
    __IO uint32_t ips_rst_1_toggle;      //0x22c
    __IO uint32_t pll_domain_rst_common; //0x230 
    __IO uint32_t pll_domain_rst_set;    //0x234
    __IO uint32_t pll_domain_rst_clear;  //0x238
    __IO uint32_t pll_domain_rst_toggle; //0x23c
    __IO uint32_t RESERVED8[48]; 
} RESET_CTL_TypeDef;

#define RST_CTL_REG  ((RESET_CTL_TypeDef *)RST_CTL_BASE)

#define RESET_CPU0   0x0
#define RESET_CPU1   0x1

//software reset
void software_reset(void);

#if 0
//CPU 0 reset flag
uint8_t get_cpu0_rst_flag(void);

//CPU 1 reset flag
uint8_t get_cpu1_rst_flag(void);

//CPU 0 neon reset flag
uint8_t get_cpu0_neon_flag(void);

//CPU 1 neon reset flag
uint8_t get_cpu1_neon_flag(void);

//CPU 0 debug reset flag
uint8_t get_cpu0_dbg_flag(void);

//CPU 1 debug reset flag
uint8_t get_cpu1_dbg_flag(void);

//CPU 0 watch-dog reset flag
uint8_t get_cpu0_wdg_flag(void);

//CPU 1 watch-dog reset flag
uint8_t get_cpu1_wdg_flag(void);

//CPU SCU reset flag
uint8_t get_cpu_scu_flag(void);

//CPU periph reset flag
uint8_t get_cpu_periph_flag(void);
#endif
//---------------------------------------------------
//cpu debug reset
//

//set
void cpu_dbg_reset_set
(
    uint8_t cpu_id
);

//clear
void cpu_dbg_reset_clear
(
    uint8_t cpu_id
);

//toggle
void cpu_dbg_reset_toggle
(
    uint8_t cpu_id
);

//common write
void cpu_dbg_reset_write
(
    uint32_t data
);

//common read
uint32_t 
cpu_dbg_reset_read(void);

//---------------------------------------------------
//cpu watchdog reset
//

//set
void cpu_wdg_reset_set
(
    uint8_t cpu_id
);

//clear
void cpu_wdg_reset_clear
(
    uint8_t cpu_id
);

//toggle
void cpu_wdg_reset_toggle
(
    uint8_t cpu_id
);

//common write
void cpu_wdg_reset_write
(
    uint32_t data
);

//common read
uint32_t 
cpu_wdg_reset_read(void);

//------------------------------------------
//AXI periph reset
//

//set
void SetAxiRst_DMA(void);
void SetAxiRst_MAC(void);
void SetAxiRst_DDR(void);
void SetAxiRst_GPV(void);

//clear
void ClearAxiRst_DMA(void);
void ClearAxiRst_MAC(void);
void ClearAxiRst_DDR(void);
void ClearAxiRst_GPV(void);

//toggle
void ToggleAxiRst_DMA(void);
void ToggleAxiRst_MAC(void);
void ToggleAxiRst_DDR(void);
void ToggleAxiRst_GPV(void);

//common write
void AxiRst_write(uint32_t data);

//common read
uint32_t AxiRst_read(void);

//------------------------------------------
//ips_0 reset
//

//set
void SetBlockRst_IPS0_B1553_0(void);
void SetBlockRst_IPS0_B1553_1(void);
void SetBlockRst_IPS0_CAN_0(void);
void SetBlockRst_IPS0_CAN_1(void);
void SetBlockRst_IPS0_CAN_2(void);
void SetBlockRst_IPS0_CAN_3(void);
void SetBlockRst_IPS0_PCA_0(void);
void SetBlockRst_IPS0_PCA_1(void);
void SetBlockRst_IPS0_PCA_2(void);
void SetBlockRst_IPS0_PCA_3(void);
void SetBlockRst_IPS0_PCA_4(void);
void SetBlockRst_IPS0_PCA_5(void);
void SetBlockRst_IPS0_UART_0(void);
void SetBlockRst_IPS0_UART_1(void);
void SetBlockRst_IPS0_UART_2(void);
void SetBlockRst_IPS0_UART_3(void);
void SetBlockRst_IPS0_UART_4(void);
void SetBlockRst_IPS0_UART_5(void);
void SetBlockRst_IPS0_UART_6(void);
void SetBlockRst_IPS0_UART_7(void);
void SetBlockRst_IPS0_UART_8(void);
void SetBlockRst_IPS0_UART_9(void);
void SetBlockRst_IPS0_UART_10(void);
void SetBlockRst_IPS0_UART_11(void);
void SetBlockRst_IPS0_QSPI(void);
void SetBlockRst_SPI_BOOT(void);

//clear
void ClearBlockRst_IPS0_B1553_0(void);
void ClearBlockRst_IPS0_B1553_1(void);
void ClearBlockRst_IPS0_CAN_0(void);
void ClearBlockRst_IPS0_CAN_1(void);
void ClearBlockRst_IPS0_CAN_2(void);
void ClearBlockRst_IPS0_CAN_3(void);
void ClearBlockRst_IPS0_PCA_0(void);
void ClearBlockRst_IPS0_PCA_1(void);
void ClearBlockRst_IPS0_PCA_2(void);
void ClearBlockRst_IPS0_PCA_3(void);
void ClearBlockRst_IPS0_PCA_4(void);
void ClearBlockRst_IPS0_PCA_5(void);
void ClearBlockRst_IPS0_UART_0(void);
void ClearBlockRst_IPS0_UART_1(void);
void ClearBlockRst_IPS0_UART_2(void);
void ClearBlockRst_IPS0_UART_3(void);
void ClearBlockRst_IPS0_UART_4(void);
void ClearBlockRst_IPS0_UART_5(void);
void ClearBlockRst_IPS0_UART_6(void);
void ClearBlockRst_IPS0_UART_7(void);
void ClearBlockRst_IPS0_UART_8(void);
void ClearBlockRst_IPS0_UART_9(void);
void ClearBlockRst_IPS0_UART_10(void);
void ClearBlockRst_IPS0_UART_11(void);
void ClearBlockRst_IPS0_QSPI(void);
void ClearBlockRst_SPI_BOOT(void);

//toggle
void ToggleBlockRst_IPS0_B1553_0(void);
void ToggleBlockRst_IPS0_B1553_1(void);
void ToggleBlockRst_IPS0_CAN_0(void);
void ToggleBlockRst_IPS0_CAN_1(void);
void ToggleBlockRst_IPS0_CAN_2(void);
void ToggleBlockRst_IPS0_CAN_3(void);
void ToggleBlockRst_IPS0_PCA_0(void);
void ToggleBlockRst_IPS0_PCA_1(void);
void ToggleBlockRst_IPS0_PCA_2(void);
void ToggleBlockRst_IPS0_PCA_3(void);
void ToggleBlockRst_IPS0_PCA_4(void);
void ToggleBlockRst_IPS0_PCA_5(void);
void ToggleBlockRst_IPS0_UART_0(void);
void ToggleBlockRst_IPS0_UART_1(void);
void ToggleBlockRst_IPS0_UART_2(void);
void ToggleBlockRst_IPS0_UART_3(void);
void ToggleBlockRst_IPS0_UART_4(void);
void ToggleBlockRst_IPS0_UART_5(void);
void ToggleBlockRst_IPS0_UART_6(void);
void ToggleBlockRst_IPS0_UART_7(void);
void ToggleBlockRst_IPS0_UART_8(void);
void ToggleBlockRst_IPS0_UART_9(void);
void ToggleBlockRst_IPS0_UART_10(void);
void ToggleBlockRst_IPS0_UART_11(void);
void ToggleBlockRst_IPS0_QSPI(void);
void ToggleBlockRst_SPI_BOOT(void);

//common write
void BlockRst_IPS0_write(uint32_t data);

//common read
uint32_t BlockRst_IPS0_read(void);

//------------------------------------------
//ips_1 reset
//

//set
void SetBlockRst_IPS1_SPI_0(void);
void SetBlockRst_IPS1_SPI_1(void);
void SetBlockRst_IPS1_SPI_2(void);
void SetBlockRst_IPS1_SPI_3(void);
void SetBlockRst_IPS1_PWM_0(void);
void SetBlockRst_IPS1_PWM_1(void);
void SetBlockRst_IPS1_PWM_2(void);
void SetBlockRst_IPS1_PWM_3(void);
void SetBlockRst_IPS1_PWM_4(void);
void SetBlockRst_IPS1_PWM_5(void);
void SetBlockRst_IPS1_PWM_6(void);
void SetBlockRst_IPS1_PWM_7(void);
void SetBlockRst_IPS1_TMR_0(void);
void SetBlockRst_IPS1_TMR_1(void);
void SetBlockRst_IPS1_TMR_2(void);
void SetBlockRst_IPS1_TMR_3(void);
void SetBlockRst_IPS1_WDG_0(void);
void SetBlockRst_IPS1_WDG_1(void);
void SetBlockRst_IPS1_I2C_0(void);
void SetBlockRst_IPS1_I2C_1(void);
void SetBlockRst_IPS1_I2C_2(void);
void SetBlockRst_IPS1_I2C_3(void);
void SetBlockRst_IPS1_GIO_0(void);
void SetBlockRst_IPS1_GIO_1(void);

//clear
void ClearBlockRst_IPS1_SPI_0(void);
void ClearBlockRst_IPS1_SPI_1(void);
void ClearBlockRst_IPS1_SPI_2(void);
void ClearBlockRst_IPS1_SPI_3(void);
void ClearBlockRst_IPS1_PWM_0(void);
void ClearBlockRst_IPS1_PWM_1(void);
void ClearBlockRst_IPS1_PWM_2(void);
void ClearBlockRst_IPS1_PWM_3(void);
void ClearBlockRst_IPS1_TMR_0(void);
void ClearBlockRst_IPS1_TMR_1(void);
void ClearBlockRst_IPS1_TMR_2(void);
void ClearBlockRst_IPS1_TMR_3(void);
void ClearBlockRst_IPS1_WDG_0(void);
void ClearBlockRst_IPS1_WDG_1(void);
void ClearBlockRst_IPS1_I2C_0(void);
void ClearBlockRst_IPS1_I2C_1(void);
void ClearBlockRst_IPS1_I2C_2(void);
void ClearBlockRst_IPS1_I2C_3(void);
void ClearBlockRst_IPS1_GIO_0(void);
void ClearBlockRst_IPS1_GIO_1(void);

//Toggle
void ToggleBlockRst_IPS1_SPI_0(void);
void ToggleBlockRst_IPS1_SPI_1(void);
void ToggleBlockRst_IPS1_SPI_2(void);
void ToggleBlockRst_IPS1_SPI_3(void);
void ToggleBlockRst_IPS1_PWM_0(void);
void ToggleBlockRst_IPS1_PWM_1(void);
void ToggleBlockRst_IPS1_PWM_2(void);
void ToggleBlockRst_IPS1_PWM_3(void);
void ToggleBlockRst_IPS1_TMR_0(void);
void ToggleBlockRst_IPS1_TMR_1(void);
void ToggleBlockRst_IPS1_TMR_2(void);
void ToggleBlockRst_IPS1_TMR_3(void);
void ToggleBlockRst_IPS1_WDG_0(void);
void ToggleBlockRst_IPS1_WDG_1(void);
void ToggleBlockRst_IPS1_I2C_0(void);
void ToggleBlockRst_IPS1_I2C_1(void);
void ToggleBlockRst_IPS1_I2C_2(void);
void ToggleBlockRst_IPS1_I2C_3(void);
void ToggleBlockRst_IPS1_GIO_0(void);
void ToggleBlockRst_IPS1_GIO_1(void);


//common write
void BlockRst_IPS1_write(uint32_t data);

//common read
uint32_t BlockRst_IPS1_read(void);

//----------------------------------------------------------
//CPU pll domain reset
//
//set
void set_cpu_pll_domain_rst(void);
//clear
void clear_cpu_pll_domain_rst(void);
//toggle
void toggle_cpu_pll_domain_rst(void);

//----------------------------------------------------------
//ddr pll domain reset
//
//set
void set_ddr_pll_domain_rst(void);
//clear
void clear_ddr_pll_domain_rst(void);
//toggle
void toggle_ddr_pll_domain_rst(void);

//common write
void write_pll_domain_rst(uint32_t data);

//common read
uint32_t read_pll_domain_rst(void);

#endif

