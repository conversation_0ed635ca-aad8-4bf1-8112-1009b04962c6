

#ifndef __TUBE_H__
#define __TUBE_H__

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdarg.h>
#include <ctype.h>

#define FPGA_TEST

#ifndef FPGA_TEST
#define  END_EXEC()	 *TUBE_BASE = 0x04
#else
#define  END_EXEC()   while(1)
#endif

void sendChar(char bData);
void sendString(char *pbString);
int vprint_var(const char * __restrict fmt, ...);
void vprint(char *fmt,...);


#endif



