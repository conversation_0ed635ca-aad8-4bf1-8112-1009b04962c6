#ifndef __CLKCTL_H__
#define __CLKCTL_H__

#include "common.h"

typedef struct
{
	__IO uint32_t L1CLKSwitch;           //0x000
	__IO uint32_t RESERVED0[3];         
	__IO uint32_t ATBCLKDiv;             //0x010
	__IO uint32_t RESERVED1[59];
	__IO uint32_t IPSCLKDiv;             //0x100
	__IO uint32_t RESERVED2[3];
	__IO uint32_t QSPICLKDiv;            //0x110
	__IO uint32_t RESERVED3[59];
	__IO uint32_t CLKGate0_common;       //0x200
	__IO uint32_t CLKGate0_set;          //0x204
	__IO uint32_t CLKGate0_clear;        //0x208
	__IO uint32_t CLKGate0_toggle;       //0x20c
	__IO uint32_t CLKGate1_common;       //0x210
	__IO uint32_t CLKGate1_set;          //0x214
	__IO uint32_t CLKGate1_clear;        //0x218
	__IO uint32_t CLKGate1_toggle;       //0x21c
	__IO uint32_t CLKGate2_common;       //0x220
	__IO uint32_t CLKGate2_set;          //0x224
	__IO uint32_t CLKGate2_clear;        //0x228
	__IO uint32_t CLKGate2_toggle;       //0x22c
	__IO uint32_t CLKGate3_common;       //0x230
	__IO uint32_t CLKGate3_set;      	 //0x234
	__IO uint32_t CLKGate3_clear;        //0x238
	__IO uint32_t CLKGate3_toggle;       //0x23c
	__IO uint32_t UARTCLK_Switch_common; //0x240
	__IO uint32_t UARTCLK_Switch_set;    //0x244
	__IO uint32_t UARTCLK_Switch_clear;  //0x248
	__IO uint32_t UARTCLK_Switch_toggle; //0x24c
	__IO uint32_t RESERVED4[44];
	__IO uint32_t CPU_PLL_0;             //0x300
	__IO uint32_t CPU_PLL_1;             //0x304
	__IO uint32_t RESERVED5[2];
	__IO uint32_t MAC_PLL_0;             //0x310
	__IO uint32_t MAC_PLL_1;             //0x314
	__IO uint32_t RESERVED6[2];
	__IO uint32_t DDR_PLL_0;             //0x320
	__IO uint32_t DDR_PLL_1;             //0x324
} CLK_CTL_TypeDef;

#define CLK_CTL_REG     ((CLK_CTL_TypeDef*)CLK_CTL_BASE)

//clk: 0(osc_cpu_clk) 1(pll_cpu_clk)
void SysClkSwitch(uint8_t clk);

//div:only support 1/2/4/8
//atb_clk = root_clk/div
void SetATBClkDiv(uint8_t div);

//div:only support 4/8/16/32
//ips_clk = root_clk/div
void SetIPSClkDiv(uint8_t div);

//return ips_clk div
int GetIPSClkDiv(void);

//div:only support 2/4/8/16
//qspi_clk = root_clk/div
void SetQSPIClkDiv(uint8_t div);

//return qspi_clk div
int GetQSPIClkDiv(void);

//-----------------------------------------
// multicore A9 ClkGate
//
// set
void Set_MPA9_CLKGate_CPU0(void);
void Set_MPA9_CLKGate_CPU1(void);
void Set_MPA9_CLKGate_CPU0_NEON(void);
void Set_MPA9_CLKGate_CPU1_NEON(void);
void Set_MPA9_CLKGate_PERIPH(void);
void Set_MPA9_CLKGate_L2C(void);
void Set_MPA9_CLKGate_ATB(void);
void Set_MPA9_CLKGate_DBGAPB(void);
void Set_MPA9_CLKGate_DAPAPB(void);
void Set_MPA9_CLKGate_DAPAHB(void);

// clear
void Clear_MPA9_CLKGate_CPU0(void);
void Clear_MPA9_CLKGate_CPU1(void);
void Clear_MPA9_CLKGate_CPU0_NEON(void);
void Clear_MPA9_CLKGate_CPU1_NEON(void);
void Clear_MPA9_CLKGate_PERIPH(void);
void Clear_MPA9_CLKGate_L2C(void);
void Clear_MPA9_CLKGate_ATB(void);
void Clear_MPA9_CLKGate_DBGAPB(void);
void Clear_MPA9_CLKGate_DAPAPB(void);
void Clear_MPA9_CLKGate_DAPAHB(void);

// toggle
void Toggle_MPA9_CLKGate_CPU0(void);
void Toggle_MPA9_CLKGate_CPU1(void);
void Toggle_MPA9_CLKGate_CPU0_NEON(void);
void Toggle_MPA9_CLKGate_CPU1_NEON(void);
void Toggle_MPA9_CLKGate_PERIPH(void);
void Toggle_MPA9_CLKGate_L2C(void);
void Toggle_MPA9_CLKGate_ATB(void);
void Toggle_MPA9_CLKGate_DBGAPB(void);
void Toggle_MPA9_CLKGate_DAPAPB(void);
void Toggle_MPA9_CLKGate_DAPAHB(void);

//common write
void Write_MPA9_CLKGate(uint32_t data);

//common read
uint32_t read_MPA9_CLKGate(void);

//-----------------------------------------
//ClkGate 1
//

//set
void Set_CLKGate_DMA(void);
void Set_CLKGate_MAC(void);
void Set_CLKGate_DDR(void);
void Set_CLKGate_GPV(void);

//clear
void Clear_CLKGate_DMA(void);
void Clear_CLKGate_MAC(void);
void Clear_CLKGate_DDR(void);
void Clear_CLKGate_GPV(void);

//toggle
void Toggle_CLKGate_DMA(void);
void Toggle_CLKGate_MAC(void);
void Toggle_CLKGate_DDR(void);
void Toggle_CLKGate_GPV(void);

//common write
void Write_CLKGate1(uint32_t data);

//common read
uint32_t Read_CLKGate1(void);

//-----------------------------------------
//ClkGate 2
//
//set
void Set_CLKGate_IPS0_1553B0(void);
void Set_CLKGate_IPS0_1553B1(void);
void Set_CLKGate_IPS0_CAN0(void);
void Set_CLKGate_IPS0_CAN1(void);
void Set_CLKGate_IPS0_CAN2(void);
void Set_CLKGate_IPS0_CAN3(void);
void Set_CLKGate_IPS0_PCA0(void);
void Set_CLKGate_IPS0_PCA1(void);
void Set_CLKGate_IPS0_PCA2(void);
void Set_CLKGate_IPS0_PCA3(void);
void Set_CLKGate_IPS0_PCA4(void);
void Set_CLKGate_IPS0_PCA5(void);
void Set_CLKGate_IPS0_UART0(void);
void Set_CLKGate_IPS0_UART1(void);
void Set_CLKGate_IPS0_UART2(void);
void Set_CLKGate_IPS0_UART3(void);
void Set_CLKGate_IPS0_UART4(void);
void Set_CLKGate_IPS0_UART5(void);
void Set_CLKGate_IPS0_UART6(void);
void Set_CLKGate_IPS0_UART7(void);
void Set_CLKGate_IPS0_UART8(void);
void Set_CLKGate_IPS0_UART9(void);
void Set_CLKGate_IPS0_UART10(void);
void Set_CLKGate_IPS0_UART11(void);
void Set_CLKGate_IPS0_QSPI(void);
void Set_CLKGate_IPS0_SPI_BOOT(void);

//clear
void Clear_CLKGate_IPS0_1553B0(void);
void Clear_CLKGate_IPS0_1553B1(void);     
void Clear_CLKGate_IPS0_CAN0(void);       
void Clear_CLKGate_IPS0_CAN1(void);       
void Clear_CLKGate_IPS0_CAN2(void);       
void Clear_CLKGate_IPS0_CAN3(void);       
void Clear_CLKGate_IPS0_PCA0(void);       
void Clear_CLKGate_IPS0_PCA1(void);       
void Clear_CLKGate_IPS0_PCA2(void);       
void Clear_CLKGate_IPS0_PCA3(void);       
void Clear_CLKGate_IPS0_PCA4(void);       
void Clear_CLKGate_IPS0_PCA5(void);       
void Clear_CLKGate_IPS0_UART0(void);      
void Clear_CLKGate_IPS0_UART1(void);      
void Clear_CLKGate_IPS0_UART2(void);      
void Clear_CLKGate_IPS0_UART3(void);      
void Clear_CLKGate_IPS0_UART4(void);      
void Clear_CLKGate_IPS0_UART5(void);     
void Clear_CLKGate_IPS0_UART6(void);      
void Clear_CLKGate_IPS0_UART7(void);      
void Clear_CLKGate_IPS0_UART8(void);      
void Clear_CLKGate_IPS0_UART9(void);      
void Clear_CLKGate_IPS0_UART10(void);     
void Clear_CLKGate_IPS0_UART11(void);     
void Clear_CLKGate_IPS0_QSPI(void);       
void Clear_CLKGate_IPS0_SPI_BOOT(void);   

//toggle
void Toggle_CLKGate_IPS0_1553B0(void);    
void Toggle_CLKGate_IPS0_1553B1(void);    
void Toggle_CLKGate_IPS0_CAN0(void);      
void Toggle_CLKGate_IPS0_CAN1(void);      
void Toggle_CLKGate_IPS0_CAN2(void);      
void Toggle_CLKGate_IPS0_CAN3(void);      
void Toggle_CLKGate_IPS0_PCA0(void);      
void Toggle_CLKGate_IPS0_PCA1(void);      
void Toggle_CLKGate_IPS0_PCA2(void);      
void Toggle_CLKGate_IPS0_PCA3(void);      
void Toggle_CLKGate_IPS0_PCA4(void);      
void Toggle_CLKGate_IPS0_PCA5(void);      
void Toggle_CLKGate_IPS0_UART0(void);     
void Toggle_CLKGate_IPS0_UART1(void);     
void Toggle_CLKGate_IPS0_UART2(void);     
void Toggle_CLKGate_IPS0_UART3(void);     
void Toggle_CLKGate_IPS0_UART4(void);     
void Toggle_CLKGate_IPS0_UART5(void);     
void Toggle_CLKGate_IPS0_UART6(void);     
void Toggle_CLKGate_IPS0_UART7(void);     
void Toggle_CLKGate_IPS0_UART8(void);     
void Toggle_CLKGate_IPS0_UART9(void);     
void Toggle_CLKGate_IPS0_UART10(void);    
void Toggle_CLKGate_IPS0_UART11(void);    
void Toggle_CLKGate_IPS0_QSPI(void);      
void Toggle_CLKGate_IPS0_SPI_BOOT(void);  

//common write
void Write_IPS0_CLKGate(uint32_t data);   

//common read
uint32_t Read_IPS0_CLKGate(void);        


//--------------------------------------
//ClkGate 3
//
//set
void Set_CLKGate_IPS1_SPI0(void);        
void Set_CLKGate_IPS1_SPI1(void);        
void Set_CLKGate_IPS1_SPI2(void);        
void Set_CLKGate_IPS1_SPI3(void);        
void Set_CLKGate_IPS1_PWM0(void);        
void Set_CLKGate_IPS1_PWM1(void);        
void Set_CLKGate_IPS1_PWM2(void);        
void Set_CLKGate_IPS1_PWM3(void);        
void Set_CLKGate_IPS1_TMR0(void);        
void Set_CLKGate_IPS1_TMR1(void);        
void Set_CLKGate_IPS1_TMR2(void);        
void Set_CLKGate_IPS1_TMR3(void);        
void Set_CLKGate_IPS1_WDG0(void);        
void Set_CLKGate_IPS1_WDG1(void);        
void Set_CLKGate_IPS1_I2C0(void);        
void Set_CLKGate_IPS1_I2C1(void);        
void Set_CLKGate_IPS1_I2C2(void);        
void Set_CLKGate_IPS1_I2C3(void);        
void Set_CLKGate_IPS1_GIO0(void);        

//clear
void Clear_CLKGate_IPS1_SPI0(void);      
void Clear_CLKGate_IPS1_SPI1(void);      
void Clear_CLKGate_IPS1_SPI2(void);      
void Clear_CLKGate_IPS1_SPI3(void);      
void Clear_CLKGate_IPS1_PWM0(void);      
void Clear_CLKGate_IPS1_PWM1(void);      
void Clear_CLKGate_IPS1_PWM2(void);      
void Clear_CLKGate_IPS1_PWM3(void);      
void Clear_CLKGate_IPS1_TMR0(void);     
void Clear_CLKGate_IPS1_TMR1(void);     
void Clear_CLKGate_IPS1_TMR2(void);     
void Clear_CLKGate_IPS1_TMR3(void);     
void Clear_CLKGate_IPS1_WDG0(void);     
void Clear_CLKGate_IPS1_WDG1(void);     
void Clear_CLKGate_IPS1_I2C0(void);     
void Clear_CLKGate_IPS1_I2C1(void);     
void Clear_CLKGate_IPS1_I2C2(void);     
void Clear_CLKGate_IPS1_I2C3(void);     
void Clear_CLKGate_IPS1_GIO0(void);     

//toggle
void Toggle_CLKGate_IPS1_SPI0(void);   
void Toggle_CLKGate_IPS1_SPI1(void);  
void Toggle_CLKGate_IPS1_SPI2(void); 
void Toggle_CLKGate_IPS1_SPI3(void); 
void Toggle_CLKGate_IPS1_PWM0(void); 
void Toggle_CLKGate_IPS1_PWM1(void); 
void Toggle_CLKGate_IPS1_PWM2(void); 
void Toggle_CLKGate_IPS1_PWM3(void);
void Toggle_CLKGate_IPS1_TMR0(void);
void Toggle_CLKGate_IPS1_TMR1(void);
void Toggle_CLKGate_IPS1_TMR2(void);
void Toggle_CLKGate_IPS1_TMR3(void);
void Toggle_CLKGate_IPS1_WDG0(void);
void Toggle_CLKGate_IPS1_WDG1(void);
void Toggle_CLKGate_IPS1_I2C0(void);
void Toggle_CLKGate_IPS1_I2C1(void);
void Toggle_CLKGate_IPS1_I2C2(void);
void Toggle_CLKGate_IPS1_I2C3(void);
void Toggle_CLKGate_IPS1_GIO0(void);

//common write
void Write_IPS1_CLKGate(uint32_t data); 

//common read
uint32_t Read_IPS1_CLKGate(void); 

//-----------------------------------------
// pll
//
// cpu_pll
void Set_CPU_PLL_0(uint8_t M, uint8_t N, uint8_t OD);
void Set_CPU_PLL_1(uint8_t BP, uint8_t RST);         

uint8_t Get_CPU_PLL_0(void);                         
uint8_t Get_CPU_PLL_1(void);                         

uint8_t Get_CPU_PLL_LOCK(void);                      

// mac_pll
void Set_MAC_PLL_0(uint8_t M, uint8_t N, uint8_t OD);
void Set_MAC_PLL_1(uint8_t BP, uint8_t RST);         

uint8_t Get_MAC_PLL_0(void);                         
uint8_t Get_MAC_PLL_1(void);                         

uint8_t Get_MAC_PLL_LOCK(void);                      

// ddr_pll
void Set_DDR_PLL_0(uint8_t M, uint8_t N, uint8_t OD);
void Set_DDR_PLL_1(uint8_t BP, uint8_t RST);         

uint8_t Get_DDR_PLL_0(void);                         
uint8_t Get_DDR_PLL_1(void);                         

uint8_t Get_DDR_PLL_LOCK(void);                      


#endif

